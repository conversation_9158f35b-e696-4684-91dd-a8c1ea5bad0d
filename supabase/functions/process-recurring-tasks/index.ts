import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Create a Supabase client with the service role key
    const supabaseAdmin = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );

    console.log('Starting recurring task processing...');

    // Find recurring tasks that are overdue for their next occurrence
    const { data: overdueTasks, error: fetchError } = await supabaseAdmin
      .from('maintenance_tasks')
      .select('*')
      .eq('is_recurring', true)
      .not('next_due_date', 'is', null)
      .lte('next_due_date', new Date().toISOString())
      .eq('status', 'completed'); // Only process completed recurring tasks

    if (fetchError) {
      console.error('Error fetching overdue recurring tasks:', fetchError);
      throw fetchError;
    }

    if (!overdueTasks || overdueTasks.length === 0) {
      console.log('No overdue recurring tasks found');
      return new Response(
        JSON.stringify({ 
          message: 'No overdue recurring tasks found',
          processed: 0
        }),
        { 
          status: 200, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    console.log(`Found ${overdueTasks.length} overdue recurring tasks`);

    const results = [];
    let successCount = 0;
    let errorCount = 0;

    for (const task of overdueTasks) {
      try {
        console.log(`Processing recurring task: ${task.id} - ${task.title}`);

        // Check if we've reached the maximum recurrences
        if (task.max_recurrences && task.recurrence_count >= task.max_recurrences) {
          console.log(`Task ${task.id} has reached maximum recurrences (${task.max_recurrences})`);
          
          // Update the task to remove next_due_date since it won't recur anymore
          await supabaseAdmin
            .from('maintenance_tasks')
            .update({ next_due_date: null })
            .eq('id', task.id);

          results.push({
            taskId: task.id,
            status: 'completed_series',
            message: 'Task series completed - reached maximum recurrences'
          });
          continue;
        }

        // Calculate the new due date
        const currentDueDate = new Date(task.next_due_date);
        const newDueDate = new Date(currentDueDate);
        newDueDate.setDate(newDueDate.getDate() + task.recurrence_interval_days);

        // Calculate the next due date after this one
        const followingDueDate = new Date(newDueDate);
        followingDueDate.setDate(followingDueDate.getDate() + task.recurrence_interval_days);

        // Create the new recurring task instance
        const newTaskData = {
          user_id: task.user_id,
          property_id: task.property_id,
          property_name: task.property_name,
          title: task.title,
          description: task.description,
          status: 'open',
          severity: task.severity,
          due_date: newDueDate.toISOString().split('T')[0], // Format as YYYY-MM-DD
          assigned_to: task.assigned_to,
          provider_id: task.provider_id,
          provider_email: task.provider_email,
          team_id: task.team_id,
          is_recurring: true,
          recurrence_interval_days: task.recurrence_interval_days,
          parent_task_id: task.parent_task_id || task.id,
          next_due_date: followingDueDate.toISOString(),
          recurrence_count: task.recurrence_count + 1,
          max_recurrences: task.max_recurrences
        };

        // Insert the new task
        const { data: newTask, error: insertError } = await supabaseAdmin
          .from('maintenance_tasks')
          .insert(newTaskData)
          .select()
          .single();

        if (insertError) {
          console.error(`Error creating new recurring task for ${task.id}:`, insertError);
          errorCount++;
          results.push({
            taskId: task.id,
            status: 'error',
            message: `Failed to create new occurrence: ${insertError.message}`
          });
          continue;
        }

        // Update the original task's next_due_date
        const { error: updateError } = await supabaseAdmin
          .from('maintenance_tasks')
          .update({ next_due_date: followingDueDate.toISOString() })
          .eq('id', task.id);

        if (updateError) {
          console.error(`Error updating next_due_date for task ${task.id}:`, updateError);
          // This is not critical, so we don't fail the whole operation
        }

        successCount++;
        results.push({
          taskId: task.id,
          newTaskId: newTask.id,
          status: 'success',
          message: `Created new occurrence #${task.recurrence_count + 1}`,
          newDueDate: newDueDate.toISOString().split('T')[0]
        });

        console.log(`Successfully created new occurrence for task ${task.id}: ${newTask.id}`);

      } catch (error) {
        console.error(`Error processing recurring task ${task.id}:`, error);
        errorCount++;
        results.push({
          taskId: task.id,
          status: 'error',
          message: `Processing failed: ${error.message}`
        });
      }
    }

    const summary = {
      message: `Processed ${overdueTasks.length} overdue recurring tasks`,
      totalProcessed: overdueTasks.length,
      successful: successCount,
      errors: errorCount,
      results
    };

    console.log('Recurring task processing completed:', summary);

    return new Response(
      JSON.stringify(summary),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('Error in process-recurring-tasks function:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});
