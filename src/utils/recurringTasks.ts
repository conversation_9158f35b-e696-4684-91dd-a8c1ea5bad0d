import { supabase } from '@/integrations/supabase/client';
import { MaintenanceTask, RecurringTaskConfig, RecurringTaskSeries } from '@/components/maintenance/types';
import { addDays, format } from 'date-fns';

/**
 * Utility functions for handling recurring maintenance tasks
 */

/**
 * Calculate the next due date for a recurring task
 */
export const calculateNextDueDate = (
  currentDueDate: string | Date,
  intervalDays: number
): Date => {
  const baseDate = typeof currentDueDate === 'string' 
    ? new Date(currentDueDate) 
    : currentDueDate;
  
  return addDays(baseDate, intervalDays);
};

/**
 * Check if a task should create a next recurrence
 */
export const shouldCreateNextRecurrence = (
  task: MaintenanceTask
): boolean => {
  if (!task.isRecurring) return false;
  
  // Check if max recurrences limit is reached
  if (task.maxRecurrences && task.recurrenceCount && 
      task.recurrenceCount >= task.maxRecurrences) {
    return false;
  }
  
  return true;
};

/**
 * Create the next recurring task instance
 */
export const createNextRecurringTask = async (
  completedTask: MaintenanceTask,
  userId: string
): Promise<string | null> => {
  if (!shouldCreateNextRecurrence(completedTask)) {
    return null;
  }

  if (!completedTask.recurrenceIntervalDays) {
    console.error('Cannot create recurring task: missing recurrence interval');
    return null;
  }

  // Calculate next due date
  const nextDueDate = calculateNextDueDate(
    completedTask.dueDate,
    completedTask.recurrenceIntervalDays
  );

  // Calculate the due date after the next one for scheduling
  const followingDueDate = calculateNextDueDate(
    nextDueDate,
    completedTask.recurrenceIntervalDays
  );

  // Create the new recurring task
  const newTaskData = {
    user_id: userId,
    property_id: completedTask.propertyId || null,
    property_name: completedTask.propertyName,
    title: completedTask.title,
    description: completedTask.description,
    status: 'open',
    severity: completedTask.severity,
    due_date: format(nextDueDate, 'yyyy-MM-dd'),
    assigned_to: completedTask.assignedTo || null,
    provider_id: completedTask.providerId || null,
    provider_email: completedTask.providerEmail || null,
    team_id: null, // Will be set by the hook if needed
    is_recurring: true,
    recurrence_interval_days: completedTask.recurrenceIntervalDays,
    parent_task_id: completedTask.parentTaskId || completedTask.id,
    next_due_date: followingDueDate.toISOString(),
    recurrence_count: (completedTask.recurrenceCount || 0) + 1,
    max_recurrences: completedTask.maxRecurrences || null
  };

  try {
    const { data, error } = await supabase
      .from('maintenance_tasks')
      .insert(newTaskData)
      .select()
      .single();

    if (error) {
      console.error('Error creating next recurring task:', error);
      return null;
    }

    console.log('Created next recurring task:', data.id);
    return data.id;
  } catch (error) {
    console.error('Error creating next recurring task:', error);
    return null;
  }
};

/**
 * Get the series of recurring tasks for a given task
 */
export const getRecurringTaskSeries = async (
  taskId: string
): Promise<RecurringTaskSeries[]> => {
  try {
    const { data, error } = await supabase
      .rpc('get_recurring_task_series', { task_id: taskId });

    if (error) {
      console.error('Error fetching recurring task series:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching recurring task series:', error);
    return [];
  }
};

/**
 * Convert database task to MaintenanceTask with recurring properties
 */
export const mapDbTaskToMaintenanceTask = (dbTask: any): MaintenanceTask => {
  return {
    id: dbTask.id,
    title: dbTask.title,
    description: dbTask.description || '',
    propertyId: dbTask.property_id,
    propertyName: dbTask.property_name,
    status: dbTask.status,
    severity: dbTask.severity,
    dueDate: dbTask.due_date || 'No due date',
    assignedTo: dbTask.assigned_to,
    createdAt: dbTask.created_at,
    providerId: dbTask.provider_id,
    providerEmail: dbTask.provider_email,
    userId: dbTask.user_id,
    // Recurring task properties
    isRecurring: dbTask.is_recurring || false,
    recurrenceIntervalDays: dbTask.recurrence_interval_days,
    parentTaskId: dbTask.parent_task_id,
    nextDueDate: dbTask.next_due_date,
    recurrenceCount: dbTask.recurrence_count || 0,
    maxRecurrences: dbTask.max_recurrences,
    completedAt: dbTask.completed_at
  };
};

/**
 * Convert MaintenanceTask to database format for recurring tasks
 */
export const mapMaintenanceTaskToDb = (task: MaintenanceTask, userId: string) => {
  return {
    user_id: userId,
    property_id: task.propertyId || null,
    property_name: task.propertyName,
    title: task.title,
    description: task.description,
    status: task.status === 'new' ? 'open' : task.status,
    severity: task.severity,
    due_date: task.dueDate === 'No due date' ? null : task.dueDate,
    assigned_to: task.assignedTo || null,
    provider_id: task.providerId || null,
    provider_email: task.providerEmail || null,
    // Recurring task properties
    is_recurring: task.isRecurring || false,
    recurrence_interval_days: task.recurrenceIntervalDays || null,
    parent_task_id: task.parentTaskId || null,
    next_due_date: task.nextDueDate ? new Date(task.nextDueDate).toISOString() : null,
    recurrence_count: task.recurrenceCount || 0,
    max_recurrences: task.maxRecurrences || null
  };
};

/**
 * Validate recurring task configuration
 */
export const validateRecurringConfig = (config: RecurringTaskConfig): string[] => {
  const errors: string[] = [];

  if (config.isRecurring) {
    if (!config.recurrenceIntervalDays || config.recurrenceIntervalDays < 1) {
      errors.push('Recurrence interval must be at least 1 day');
    }

    if (config.recurrenceIntervalDays > 365) {
      errors.push('Recurrence interval cannot exceed 365 days');
    }

    if (config.maxRecurrences && config.maxRecurrences < 1) {
      errors.push('Maximum recurrences must be at least 1');
    }

    if (config.maxRecurrences && config.maxRecurrences > 1000) {
      errors.push('Maximum recurrences cannot exceed 1000');
    }
  }

  return errors;
};

/**
 * Format recurrence description for display
 */
export const formatRecurrenceDescription = (
  intervalDays: number,
  maxRecurrences?: number
): string => {
  let description = '';

  if (intervalDays === 1) {
    description = 'Daily';
  } else if (intervalDays === 7) {
    description = 'Weekly';
  } else if (intervalDays === 14) {
    description = 'Every 2 weeks';
  } else if (intervalDays === 30) {
    description = 'Monthly';
  } else if (intervalDays === 90) {
    description = 'Quarterly';
  } else if (intervalDays === 180) {
    description = 'Every 6 months';
  } else if (intervalDays === 365) {
    description = 'Annually';
  } else {
    description = `Every ${intervalDays} days`;
  }

  if (maxRecurrences) {
    description += ` (${maxRecurrences} times)`;
  } else {
    description += ' (indefinitely)';
  }

  return description;
};
