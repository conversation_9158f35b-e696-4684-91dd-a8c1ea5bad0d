import React from 'react';
import {
  ButtonGroup,
  ActionButton,
  PageHeaderButtons,
  FormButtons,
  DataTableButtons
} from '@/components/ui/ButtonStandardization';
import {
  SearchBar,
  FilterBar,
  FilterDropdown,
  FilterOption,
  FilterPanel,
  ActiveFilters
} from '@/components/ui/FilterStandardization';
import { Button } from '@/components/ui/button';
import {
  Plus,
  Filter,
  RefreshCw,
  Grid3X3,
  List,
  Search,
  ArrowRight,
  Download,
  Upload,
  Trash,
  Edit,
  Save,
  Trash2
} from 'lucide-react';
import { clearCacheAndRefresh } from '@/utils/cacheUtils';

/**
 * StandardPageHeader - A standardized page header with title, description, search, and action buttons
 */
export const StandardPageHeader: React.FC<{
  title: string;
  description?: string;
  searchQuery?: string;
  onSearchChange?: (value: string) => void;
  searchPlaceholder?: string;
  onRefresh?: () => void;
  isLoading?: boolean;
  onToggleFilters?: () => void;
  filtersOpen?: boolean;
  onToggleView?: (view: 'grid' | 'list') => void;
  viewType?: 'grid' | 'list';
  primaryActionLabel?: string;
  onPrimaryAction?: () => void;
  primaryActionIcon?: React.ReactNode;
  secondaryActions?: React.ReactNode;
  itemCount?: number;
  showViewToggle?: boolean;
  showSearch?: boolean;
  showFilterToggle?: boolean;
}> = ({
  title,
  description,
  searchQuery = '',
  onSearchChange,
  searchPlaceholder = 'Search...',
  onRefresh,
  isLoading = false,
  onToggleFilters,
  filtersOpen = false,
  onToggleView,
  viewType = 'grid',
  primaryActionLabel,
  onPrimaryAction,
  primaryActionIcon = <Plus className="h-4 w-4 mr-2" />,
  secondaryActions,
  itemCount,
  showViewToggle = false,
  showSearch = true,
  showFilterToggle = true,
}) => {
  return (
    <div className="space-y-4 mb-6">
      {/* Mobile-optimized header */}
      <div className="flex flex-col gap-4">
        {/* Title and description */}
        <div>
          <h1 className="text-xl sm:text-2xl font-bold tracking-tight">{title}</h1>
          {description && (
            <p className="text-muted-foreground mt-1 text-sm sm:text-base">
              {description} {itemCount !== undefined && `(${itemCount})`}
            </p>
          )}
        </div>

        <PageHeaderButtons>
            {/* Primary action button */}
            {primaryActionLabel && onPrimaryAction && (
              <Button
                onClick={onPrimaryAction}
                size="sm"
                className="flex-shrink-0"
              >
                {primaryActionIcon}
                {primaryActionLabel}
              </Button>
            )}

            {onRefresh && (
              <Button
                variant="outline"
                size="sm"
                onClick={onRefresh}
                disabled={isLoading}
                aria-label="Refresh"
                title="Refresh"
                className="flex-shrink-0"
              >
                <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                <span className="sr-only">Refresh</span>
              </Button>
            )}

            {/* View toggle - hide on mobile since inventory forces grid anyway */}
            {showViewToggle && onToggleView && (
              <div className="hidden sm:flex border rounded-md overflow-hidden">
                <Button
                  variant={viewType === 'grid' ? "default" : "outline"}
                  size="sm"
                  onClick={() => onToggleView('grid')}
                  className="rounded-none border-0"
                >
                  <Grid3X3 className="h-4 w-4" />
                  <span className="sr-only">Grid view</span>
                </Button>
                <Button
                  variant={viewType === 'list' ? "default" : "outline"}
                  size="sm"
                  onClick={() => onToggleView('list')}
                  className="rounded-none border-0"
                >
                  <List className="h-4 w-4" />
                  <span className="sr-only">List view</span>
                </Button>
              </div>
            )}
        </PageHeaderButtons>

          {/* Secondary actions in a separate row if they exist */}
          {secondaryActions && (
            <div className="flex flex-wrap gap-2">
              {secondaryActions}
            </div>
          )}
        </div>

      {/* Search bar - full width and prominent on mobile */}
      {showSearch && onSearchChange && (
        <div className="w-full">
          <SearchBar
            value={searchQuery || ''}
            onChange={onSearchChange}
            placeholder={searchPlaceholder}
            className="w-full md:max-w-md"
          />
        </div>
      )}
    </div>
  );
};

/**
 * StandardEmptyState - A standardized empty state component
 */
export const StandardEmptyState: React.FC<{
  title: string;
  description: string;
  icon?: React.ReactNode;
  actionLabel?: string;
  onAction?: () => void;
  actionIcon?: React.ReactNode;
}> = ({
  title,
  description,
  icon,
  actionLabel,
  onAction,
  actionIcon = <Plus className="h-4 w-4 mr-2" />,
}) => {
  return (
    <div className="text-center py-16 border-2 border-dashed border-border rounded-lg">
      {icon && <div className="mx-auto mb-4">{icon}</div>}
      <h3 className="text-xl font-semibold mb-2">{title}</h3>
      <p className="text-muted-foreground mb-6 max-w-md mx-auto">
        {description}
      </p>
      {actionLabel && onAction && (
        <Button onClick={onAction}>
          {actionIcon}
          {actionLabel}
        </Button>
      )}
    </div>
  );
};

/**
 * StandardLoadingState - A standardized loading state component
 */
export const StandardLoadingState: React.FC<{
  message?: string;
  overlay?: boolean; // Add overlay option for loading over content
  mini?: boolean; // Add mini option for a smaller loading indicator
}> = ({
  message = 'Loading...',
  overlay = false,
  mini = false
}) => {
  if (overlay) {
    return (
      <div className="absolute inset-0 flex flex-col items-center justify-center bg-background/80 z-10">
        <RefreshCw className="h-8 w-8 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">{message}</p>
      </div>
    );
  }
  
  if (mini) {
    return (
      <div className="flex items-center justify-center gap-2 py-2">
        <RefreshCw className="h-4 w-4 animate-spin text-primary" />
        <p className="text-xs text-muted-foreground">{message}</p>
      </div>
    );
  }
  
  return (
    <div className="flex flex-col items-center justify-center py-16">
      <RefreshCw className="h-8 w-8 animate-spin text-primary mb-4" />
      <p className="text-muted-foreground">{message}</p>
    </div>
  );
};

/**
 * StandardErrorState - A standardized error state component
 */
export const StandardErrorState: React.FC<{
  message?: string;
  retryLabel?: string;
  onRetry?: () => void;
}> = ({
  message = 'An error occurred',
  retryLabel = 'Retry',
  onRetry,
}) => {
  return (
    <div className="flex flex-col items-center justify-center py-16">
      <div className="text-destructive mb-4">{message}</div>
      {onRetry && (
        <Button onClick={onRetry} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          {retryLabel}
        </Button>
      )}
    </div>
  );
};

/**
 * StandardViewToggle - A standardized view toggle component
 */
export const StandardViewToggle: React.FC<{
  viewType: 'grid' | 'list';
  onToggleView: (view: 'grid' | 'list') => void;
}> = ({
  viewType,
  onToggleView,
}) => {
  return (
    <div className="flex border rounded-md overflow-hidden">
      <Button
        variant={viewType === 'grid' ? "default" : "outline"}
        size="sm"
        onClick={() => onToggleView('grid')}
        className="rounded-none border-0"
      >
        <Grid3X3 className="h-4 w-4" />
        <span className="sr-only">Grid view</span>
      </Button>
      <Button
        variant={viewType === 'list' ? "default" : "outline"}
        size="sm"
        onClick={() => onToggleView('list')}
        className="rounded-none border-0"
      >
        <List className="h-4 w-4" />
        <span className="sr-only">List view</span>
      </Button>
    </div>
  );
};

/**
 * StandardTableActions - A standardized component for table row actions
 */
export const StandardTableActions: React.FC<{
  onEdit?: () => void;
  onDelete?: () => void;
  onView?: () => void;
  viewLabel?: string;
  editLabel?: string;
  deleteLabel?: string;
  showLabels?: boolean;
}> = ({
  onEdit,
  onDelete,
  onView,
  viewLabel = 'View',
  editLabel = 'Edit',
  deleteLabel = 'Delete',
  showLabels = false,
}) => {
  return (
    <DataTableButtons>
      {onView && (
        <Button variant="ghost" size="sm" onClick={onView}>
          <ArrowRight className="h-4 w-4" />
          {showLabels && <span className="ml-2">{viewLabel}</span>}
        </Button>
      )}
      {onEdit && (
        <Button variant="ghost" size="sm" onClick={onEdit}>
          <Edit className="h-4 w-4" />
          {showLabels && <span className="ml-2">{editLabel}</span>}
        </Button>
      )}
      {onDelete && (
        <Button variant="ghost" size="sm" onClick={onDelete} className="text-destructive">
          <Trash className="h-4 w-4" />
          {showLabels && <span className="ml-2">{deleteLabel}</span>}
        </Button>
      )}
    </DataTableButtons>
  );
};

export {
  ButtonGroup,
  ActionButton,
  PageHeaderButtons,
  FormButtons,
  DataTableButtons,
  SearchBar,
  FilterBar,
  FilterDropdown,
  FilterOption,
  FilterPanel,
  ActiveFilters
};
