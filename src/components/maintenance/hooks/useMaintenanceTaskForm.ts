import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useMaintenanceTasksQueryV2 } from '@/hooks/useMaintenanceTasksQueryV2';
import { MaintenanceTask, MaintenanceStatus, RecurringTaskConfig } from '@/components/maintenance/types';
import { Provider } from '@/components/maintenance/types';
import { useAuth } from '@/contexts/AuthContext';
import { validateRecurringConfig, calculateNextDueDate } from '@/utils/recurringTasks';
import { toast } from 'sonner';

interface Property {
  id: string;
  name: string;
  address: string;
}

interface FormState {
  title: string;
  setTitle: (title: string) => void;
  description: string;
  setDescription: (description: string) => void;
  selectedPropertyId: string;
  setSelectedPropertyId: (id: string) => void;
  severity: MaintenanceTask['severity'];
  setSeverity: (severity: MaintenanceTask['severity']) => void;
  status: MaintenanceTask['status'];
  setStatus: (status: MaintenanceTask['status']) => void;
  dueDate: string;
  setDueDate: (date: string) => void;
  selectedProviderId: string;
  setSelectedProviderId: (id: string) => void;
  assignedTo: string | null;
  setAssignedTo: (assignedTo: string) => void;
  properties: Property[];
  isLoading: boolean;
  nextCheckInDate: string | null;
  checkOutDate: string | null;
  isOccupied: boolean;
  isSyncingCalendar: boolean;
  syncCalendarData: () => Promise<boolean>;
  // Recurring task properties
  recurringConfig: RecurringTaskConfig;
  setRecurringConfig: (config: RecurringTaskConfig) => void;
}

export const useMaintenanceTaskForm = (
  onSuccess: () => void,
  onClose: (open: boolean) => void,
  providers: Provider[] = [],
  initialTask?: MaintenanceTask,
  setErrorMessage?: (error: string | null) => void
) => {
  const [title, setTitle] = useState(initialTask?.title || '');
  const [description, setDescription] = useState(initialTask?.description || '');
  const [selectedPropertyId, setSelectedPropertyId] = useState(initialTask?.propertyId || '');
  const [severity, setSeverity] = useState<MaintenanceTask['severity']>(initialTask?.severity || 'medium');
  const [status, setStatus] = useState<MaintenanceTask['status']>(initialTask?.status || 'new');
  const [dueDate, setDueDate] = useState(initialTask?.dueDate && initialTask.dueDate !== 'No due date' ? initialTask.dueDate : '');
  const [selectedProviderId, setSelectedProviderId] = useState(initialTask?.providerId || '');
  const [assignedTo, setAssignedTo] = useState<string | null>(initialTask?.assignedTo || '');
  const [properties, setProperties] = useState<Property[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [nextCheckInDate, setNextCheckInDate] = useState<string | null>(null);
  const [checkOutDate, setCheckOutDate] = useState<string | null>(null);
  const [isOccupied, setIsOccupied] = useState(false);
  const [isSyncingCalendar, setIsSyncingCalendar] = useState(false);

  // Recurring task state
  const [recurringConfig, setRecurringConfig] = useState<RecurringTaskConfig>({
    isRecurring: initialTask?.isRecurring || false,
    recurrenceIntervalDays: initialTask?.recurrenceIntervalDays || 30,
    maxRecurrences: initialTask?.maxRecurrences
  });

  const { addTask, updateTask } = useMaintenanceTasksQueryV2();
  const { authState } = useAuth();
  const userId = authState.user?.id;

  useEffect(() => {
    const fetchProperties = async () => {
      if (!userId) return;

      try {
        // First, check if the user is a super admin or admin
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('is_super_admin, role')
          .eq('id', userId)
          .single();

        if (profileError) {
          console.error('Error fetching user profile:', profileError);
          throw profileError;
        }

        const isSuperAdmin = profileData?.is_super_admin || profileData?.role === 'admin';
        const isServiceProvider = profileData?.role === 'service_provider';

        let properties: Property[] = [];

        // For super admins, fetch all properties
        if (isSuperAdmin) {
          const { data, error } = await supabase
            .from('properties')
            .select('id, name, address')
            .order('name');

          if (error) throw new Error(error.message);
          properties = data || [];
        } else {
          console.log('Fetching properties for user:', userId);

          try {
            // Use the unified get_user_role_properties function that handles all user roles
            const { data: rpcData, error: rpcError } = await supabase.rpc(
              'get_user_role_properties',
              { p_user_id: userId }
            );

            if (rpcError) {
              console.error('Error using get_user_role_properties RPC:', rpcError);
              throw rpcError;
            }

            properties = rpcData || [];
          } catch (rpcErr) {
            console.error('Exception with get_user_role_properties:', rpcErr);

            // Fallback: Get properties from teams the user is a member of
            const { data: teamData, error: teamError } = await supabase
              .from('team_members')
              .select('team_id')
              .eq('user_id', userId)
              .eq('status', 'active');

            if (teamError) throw teamError;

            if (teamData && teamData.length > 0) {
              const teamIds = teamData.map(t => t.team_id);
              console.log('User is member of teams:', teamIds);

              // Get properties associated with these teams
              const { data: teamProperties, error: propError } = await supabase
                .from('team_properties')
                .select('property_id')
                .in('team_id', teamIds);

              if (propError) throw propError;

              if (teamProperties && teamProperties.length > 0) {
                const propertyIds = teamProperties.map(tp => tp.property_id);
                console.log('Found property IDs from teams:', propertyIds);

                // Get the actual property details
                const { data: propertyData, error: detailsError } = await supabase
                  .from('properties')
                  .select('id, name, address')
                  .in('id', propertyIds)
                  .order('name');

                if (detailsError) throw detailsError;
                properties = propertyData || [];
              }
            }

            // If no team properties found, try to get user's own properties
            if (properties.length === 0) {
              const { data, error } = await supabase
                .from('properties')
                .select('id, name, address')
                .eq('user_id', userId)
                .order('name');

              if (error) throw new Error(error.message);
              properties = data || [];
            }
          }
        }

        console.log(`Fetched ${properties.length} properties for user ${userId} with role ${profileData?.role}`);
        setProperties(properties);
      } catch (error) {
        console.error('Error fetching properties:', error);
        if (setErrorMessage) {
          setErrorMessage((error as Error).message);
        }
      }
    };

    fetchProperties();
  }, [userId, setErrorMessage]);

  useEffect(() => {
    const fetchPropertyDetails = async () => {
      if (!selectedPropertyId || selectedPropertyId === 'none') {
        setNextCheckInDate(null);
        setCheckOutDate(null);
        setIsOccupied(false);
        return;
      }

      try {
        const { data, error } = await supabase
          .from('properties')
          .select(`
            id,
            name,
            address,
            next_booking,
            is_occupied,
            current_checkout,
            next_checkin_date,
            next_checkin_formatted,
            ical_url
          `)
          .eq('id', selectedPropertyId)
          .single();

        if (error) {
          throw error;
        }

        if (data) {
          console.log('Property data loaded:', data);

          // Set occupancy status
          setIsOccupied(!!data.is_occupied);

          // Set checkout date if property is occupied
          if (data.current_checkout) {
            setCheckOutDate(data.current_checkout);
          } else {
            setCheckOutDate(null);
          }

          // Set next check-in date in order of preference
          if (data.next_checkin_formatted) {
            setNextCheckInDate(data.next_checkin_formatted);
          } else if (data.next_checkin_date) {
            try {
              const date = new Date(data.next_checkin_date);
              const formatted = date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              });
              setNextCheckInDate(formatted);
            } catch (e) {
              console.error('Error formatting next_checkin_date:', e);
              setNextCheckInDate(data.next_checkin_date);
            }
          } else if (data.next_booking) {
            // Try to extract the check-in date from next_booking string
            const match = data.next_booking.match(/(\w+ \d+, \d{4})/);
            if (match) {
              setNextCheckInDate(match[1]);
            } else {
              setNextCheckInDate(data.next_booking);
            }
          } else {
            setNextCheckInDate(null);
          }
        }
      } catch (error) {
        console.error('Error fetching property details:', error);
        toast.error('Failed to load property details');
        setNextCheckInDate(null);
        setCheckOutDate(null);
        setIsOccupied(false);
      }
    };

    fetchPropertyDetails();
  }, [selectedPropertyId]);

  const syncCalendarData = async () => {
    if (!selectedPropertyId || selectedPropertyId === 'none') {
      return false;
    }

    setIsSyncingCalendar(true);

    try {
      // Get the property to find its iCal URL
      const { data: property, error: propertyError } = await supabase
        .from('properties')
        .select('ical_url')
        .eq('id', selectedPropertyId)
        .single();

      if (propertyError) throw propertyError;
      if (!property?.ical_url) {
        toast.error('No iCal URL found for this property');
        return false;
      }

      // Call the fetch-ical-data function
      const { data, error } = await supabase.functions.invoke('fetch-ical-data', {
        body: {
          url: property.ical_url,
          propertyId: selectedPropertyId,
          userId
        }
      });

      if (error) throw error;

      if (data.success) {
        toast.success('Calendar synced successfully');

        // Refetch property details to update UI
        const { data: updatedProperty, error: fetchError } = await supabase
          .from('properties')
          .select(`
            id,
            next_booking,
            is_occupied,
            current_checkout,
            next_checkin_date,
            next_checkin_formatted
          `)
          .eq('id', selectedPropertyId)
          .single();

        if (fetchError) throw fetchError;

        if (updatedProperty) {
          setIsOccupied(!!updatedProperty.is_occupied);
          setCheckOutDate(updatedProperty.current_checkout || null);
          setNextCheckInDate(updatedProperty.next_checkin_formatted || updatedProperty.next_booking || null);
        }

        return true;
      } else {
        throw new Error(data.error || 'Unknown error syncing calendar');
      }
    } catch (error) {
      console.error('Error syncing calendar:', error);
      toast.error(`Failed to sync calendar: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return false;
    } finally {
      setIsSyncingCalendar(false);
    }
  };

  const resetForm = () => {
    setTitle('');
    setDescription('');
    setSelectedPropertyId('');
    setSeverity('medium');
    setStatus('new');
    setDueDate('');
    setSelectedProviderId('');
    setAssignedTo('');
    setNextCheckInDate(null);
    setCheckOutDate(null);
    setIsOccupied(false);
    setRecurringConfig({
      isRecurring: false,
      recurrenceIntervalDays: 30,
      maxRecurrences: undefined
    });
    if (setErrorMessage) {
      setErrorMessage(null);
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!title || !description) {
      if (setErrorMessage) {
        setErrorMessage('Title and description are required');
      }
      return null;
    }

    // Validate recurring task configuration
    if (recurringConfig.isRecurring) {
      const validationErrors = validateRecurringConfig(recurringConfig);
      if (validationErrors.length > 0) {
        if (setErrorMessage) {
          setErrorMessage(validationErrors.join(', '));
        }
        return null;
      }
    }

    setIsLoading(true);

    try {
      let selectedProperty;
      if (selectedPropertyId && selectedPropertyId !== 'none') {
        selectedProperty = properties.find(p => p.id === selectedPropertyId);
      }

      let selectedProvider;
      if (selectedProviderId && selectedProviderId !== 'none') {
        console.log('Looking for provider with ID:', selectedProviderId);
        console.log('Available providers:', providers);
        selectedProvider = providers.find(p => p.id === selectedProviderId);
        console.log('Selected provider:', selectedProvider);
      }

      const taskData = {
        title,
        description,
        propertyId: selectedPropertyId !== 'none' ? selectedPropertyId : undefined,
        propertyName: selectedProperty ? selectedProperty.name : undefined,
        severity,
        status: initialTask ? status : 'new',
        dueDate: dueDate || 'No due date',
        assignedTo: assignedTo ? assignedTo : undefined,
        providerId: selectedProviderId !== 'none' ? selectedProviderId : undefined,
        providerEmail: selectedProvider?.email
      } as MaintenanceTask;

      let result;

      if (initialTask) {
        result = await updateTask(initialTask.id, taskData);
      } else {
        result = await addTask(taskData);
      }

      if (result) {
        resetForm();
        onSuccess();
        onClose(false);
        // Return the task data with ID for email notifications
        return {
          ...taskData,
          id: result.id
        };
      } else {
        throw new Error('Failed to save task');
      }
    } catch (error) {
      console.error('Error saving task:', error);
      toast.error(`Failed to ${initialTask ? 'update' : 'create'} task`);
      if (setErrorMessage) {
        setErrorMessage(`Error: ${(error as Error).message}`);
      }
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const formState: FormState = {
    title,
    setTitle,
    description,
    setDescription,
    selectedPropertyId,
    setSelectedPropertyId,
    severity,
    setSeverity,
    status,
    setStatus,
    dueDate,
    setDueDate,
    selectedProviderId,
    setSelectedProviderId,
    assignedTo,
    setAssignedTo,
    properties,
    isLoading,
    nextCheckInDate,
    checkOutDate,
    isOccupied,
    isSyncingCalendar,
    syncCalendarData
  };

  return {
    formState,
    handleSubmit,
    resetForm,
    syncCalendarData
  };
};
