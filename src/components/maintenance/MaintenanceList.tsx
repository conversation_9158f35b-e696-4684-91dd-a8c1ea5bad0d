
import React from 'react';
import { MaintenanceTask, MaintenanceStatus } from './types';
import MaintenanceCard from './MaintenanceCard';
import { Skeleton } from '@/components/ui/skeleton';

interface MaintenanceListProps {
  tasks: MaintenanceTask[];
  loading?: boolean;
  filter?: string;
  onTaskClick?: (task: MaintenanceTask) => void;
  onStatusChange?: (taskId: string, newStatus: MaintenanceStatus) => Promise<boolean>;
  onDeleteTask?: (taskId: string) => Promise<boolean>;
}

const MaintenanceList: React.FC<MaintenanceListProps> = ({
  tasks,
  loading = false,
  filter,
  onTaskClick,
  onStatusChange,
  onDeleteTask
}) => {
  // Filter tasks based on status
  const filteredTasks = filter
    ? tasks.filter(task => task.status === filter)
    : tasks;

  // If loading and we have no tasks, show skeletons
  if (loading && filteredTasks.length === 0) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((_, index) => (
          <Skeleton key={index} className="h-32 w-full rounded-md" />
        ))}
      </div>
    );
  }

  // If we have tasks, show them even during loading to prevent disappearing data
  // This ensures users always see something while data refreshes
  // We'll show a subtle loading indicator in the return block

  if (filteredTasks.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 px-4 text-center">
        <p className="text-gray-500 text-lg mb-2">No maintenance tasks found</p>
        <p className="text-gray-400 text-sm max-w-md">
          {filter
            ? `There are no tasks with status "${filter}"`
            : "Create a new maintenance task to get started"
          }
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Show a subtle loading indicator at the top when refreshing data but we have existing tasks */}
      {loading && filteredTasks.length > 0 && (
        <div className="bg-blue-50 text-blue-700 dark:bg-blue-950/20 dark:text-blue-400 p-2 rounded-md text-sm flex items-center justify-center">
          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-blue-700 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Refreshing tasks...
        </div>
      )}

      {filteredTasks.map(task => (
        <MaintenanceCard
          key={task.id}
          task={task}
          onClick={() => onTaskClick && onTaskClick(task)}
          onStatusChange={onStatusChange}
          onDeleteTask={onDeleteTask}
        />
      ))}
    </div>
  );
};

export default MaintenanceList;
