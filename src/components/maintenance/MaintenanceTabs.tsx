
import React, { useState, useMemo } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import MaintenanceList from './MaintenanceList';
import ProviderManagement from './ProviderManagement';
import { MaintenanceTask, MaintenanceStatus, MaintenanceSeverity } from './types';
import { Provider } from './types';
import Tooltip from '@/components/ui/Tooltip';
import { Button } from '@/components/ui/button';
import { ArrowUpDown, ArrowDown, ArrowUp, Calendar, Clock, AlertTriangle, Printer, Repeat } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import PrintDialog from './PrintDialog';
import { SearchBar } from '@/components/ui/FilterStandardization';

interface MaintenanceTabsProps {
  tasks: MaintenanceTask[];
  providers?: Provider[];
  loading?: boolean;
  onTaskClick?: (task: MaintenanceTask) => void;
  onAddProvider?: (provider: Omit<Provider, 'id'>) => void;
  onUpdateProvider?: (id: string, provider: Omit<Provider, 'id'>) => void;
  onDeleteProvider?: (id: string) => void;
  onStatusChange?: (taskId: string, newStatus: MaintenanceStatus) => Promise<boolean>;
  onDeleteTask?: (taskId: string) => Promise<boolean>;
  actionButtons?: React.ReactNode;
}

// Sort types
type SortField = 'dueDate' | 'severity' | 'createdAt' | 'status';
type SortDirection = 'asc' | 'desc';

interface SortOption {
  field: SortField;
  direction: SortDirection;
  label: string;
  icon: React.ReactNode;
}

const MaintenanceTabs: React.FC<MaintenanceTabsProps> = ({
  tasks,
  providers = [],
  loading,
  onTaskClick,
  onAddProvider,
  onUpdateProvider,
  onDeleteProvider,
  onStatusChange,
  onDeleteTask,
  actionButtons
}) => {
  const [activeTab, setActiveTab] = useState("tasks");
  const [isPrintDialogOpen, setIsPrintDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | undefined>(() => {
    // Check for URL parameter in session storage
    const savedFilter = window.sessionStorage.getItem('maintenance_status_filter');
    if (savedFilter) {
      // Clear it after reading
      window.sessionStorage.removeItem('maintenance_status_filter');
      return savedFilter;
    }
    return undefined; // Default to "All Tasks"
  });

  const [severityFilter, setSeverityFilter] = useState<MaintenanceSeverity | undefined>(() => {
    // Check for URL parameter in session storage
    const savedFilter = window.sessionStorage.getItem('maintenance_severity_filter') as MaintenanceSeverity | null;
    if (savedFilter) {
      // Clear it after reading
      window.sessionStorage.removeItem('maintenance_severity_filter');
      return savedFilter;
    }
    return undefined; // Default
  });

  const [providerFilter, setProviderFilter] = useState<string | undefined>(() => {
    // Check for URL parameter in session storage
    const savedFilter = window.sessionStorage.getItem('maintenance_provider_filter');
    if (savedFilter) {
      // Clear it after reading
      window.sessionStorage.removeItem('maintenance_provider_filter');
      return savedFilter;
    }
    return undefined; // Default
  });

  const [propertyFilter, setPropertyFilter] = useState<string | undefined>(() => {
    // Check for URL parameter in session storage
    const savedFilter = window.sessionStorage.getItem('maintenance_property_filter');
    if (savedFilter) {
      // Clear it after reading
      window.sessionStorage.removeItem('maintenance_property_filter');
      return savedFilter;
    }
    return undefined; // Default
  });

  const [sortConfig, setSortConfig] = useState<{field: SortField, direction: SortDirection}>({field: 'dueDate', direction: 'asc'});
  const [showCompleted, setShowCompleted] = useState(() => {
    // Check for URL parameter in session storage
    const savedSetting = window.sessionStorage.getItem('maintenance_show_completed');
    if (savedSetting) {
      // Clear it after reading
      window.sessionStorage.removeItem('maintenance_show_completed');
      return savedSetting === 'true';
    }
    return false; // Default
  });

  const [showRecurring, setShowRecurring] = useState(false);

  // Debug recurring filter
  console.log('[MaintenanceTabs] showRecurring:', showRecurring, 'tasks with isRecurring:', tasks.filter(t => t.isRecurring).length);

  // Get unique properties from tasks for property filter
  const properties = useMemo(() => {
    const uniqueProperties = new Map<string, {id: string, name: string}>();

    tasks.forEach(task => {
      if (task.propertyId && task.propertyName) {
        uniqueProperties.set(task.propertyId, {
          id: task.propertyId,
          name: task.propertyName
        });
      }
    });

    return Array.from(uniqueProperties.values());
  }, [tasks]);

  // Status filter options
  const filterOptions = [
    { value: undefined, label: 'All Tasks', tooltip: 'View all maintenance tasks regardless of status' },
    { value: 'new', label: 'New', tooltip: 'Tasks that have been created but not yet assigned' },
    { value: 'assigned', label: 'Assigned', tooltip: 'Tasks that have been assigned to a service provider' },
    { value: 'accepted', label: 'Accepted', tooltip: 'Tasks that have been accepted by a service provider' },
    { value: 'in_progress', label: 'In Progress', tooltip: 'Tasks currently being worked on' },
    { value: 'completed', label: 'Completed', tooltip: 'Tasks that have been finished' }
  ];

  // Severity filter options
  const severityOptions: { value: MaintenanceSeverity | undefined; label: string; tooltip: string }[] = [
    { value: undefined, label: 'All Priorities', tooltip: 'View tasks of all priority levels' },
    { value: 'critical', label: 'Critical', tooltip: 'Urgent tasks that need immediate attention' },
    { value: 'high', label: 'High', tooltip: 'Important tasks that should be addressed soon' },
    { value: 'medium', label: 'Medium', tooltip: 'Standard priority tasks' },
    { value: 'low', label: 'Low', tooltip: 'Tasks that can be addressed when time permits' }
  ];

  // Sort options
  const sortOptions: SortOption[] = [
    { field: 'dueDate', direction: 'asc', label: 'Due Date (Earliest)', icon: <Calendar className="h-4 w-4 mr-2" /> },
    { field: 'dueDate', direction: 'desc', label: 'Due Date (Latest)', icon: <Calendar className="h-4 w-4 mr-2" /> },
    { field: 'severity', direction: 'desc', label: 'Priority (Highest)', icon: <AlertTriangle className="h-4 w-4 mr-2" /> },
    { field: 'severity', direction: 'asc', label: 'Priority (Lowest)', icon: <AlertTriangle className="h-4 w-4 mr-2" /> },
    { field: 'createdAt', direction: 'desc', label: 'Newest First', icon: <Clock className="h-4 w-4 mr-2" /> },
    { field: 'createdAt', direction: 'asc', label: 'Oldest First', icon: <Clock className="h-4 w-4 mr-2" /> },
  ];

  // Handle sorting
  const handleSort = (field: SortField, direction: SortDirection) => {
    setSortConfig({ field, direction });
  };

  // Get current sort option label
  const currentSortLabel = useMemo(() => {
    const option = sortOptions.find(opt =>
      opt.field === sortConfig.field && opt.direction === sortConfig.direction
    );
    return option ? option.label : 'Sort';
  }, [sortConfig, sortOptions]);

  // Filter and sort tasks
  const processedTasks = useMemo(() => {
    // Start with all tasks
    let filteredTasks = [...tasks];

    // Filter by search query if provided
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filteredTasks = filteredTasks.filter(task => {
        // Search in title, description, property name, provider name, and assigned to
        const searchableFields = [
          task.title,
          task.description,
          task.propertyName,
          task.assignedTo || '',
          // Find provider name from providers array
          providers.find(p => p.id === task.providerId)?.name || ''
        ].filter(Boolean); // Remove empty/undefined values
        
        return searchableFields.some(field => 
          field.toLowerCase().includes(query)
        );
      });
    }

    // Filter by status if a status filter is selected
    if (statusFilter) {
      filteredTasks = filteredTasks.filter(task => task.status === statusFilter);
    }

    // Filter by severity if a severity filter is selected
    if (severityFilter) {
      filteredTasks = filteredTasks.filter(task => task.severity === severityFilter);
    }

    // Filter by provider if a provider filter is selected
    if (providerFilter) {
      filteredTasks = filteredTasks.filter(task => task.providerId === providerFilter);
    }

    // Filter by property if a property filter is selected
    if (propertyFilter) {
      filteredTasks = filteredTasks.filter(task => task.propertyId === propertyFilter);
    }

    // Handle recurring task filtering
    if (showRecurring) {
      // Show only recurring tasks (both active and completed)
      filteredTasks = filteredTasks.filter(task => task.isRecurring);
    } else {
      // Normal filtering - hide completed tasks
      if (!showCompleted && statusFilter !== 'completed') {
        filteredTasks = filteredTasks.filter(task => {
          // Hide all completed and cancelled tasks
          if (task.status === 'completed' || task.status === 'cancelled') {
            return false;
          }

          // For recurring tasks, also check if they're due yet
          if (task.isRecurring && task.dueDate && task.dueDate !== 'No due date') {
            const dueDate = new Date(task.dueDate);
            const now = new Date();

            // Only show recurring tasks that are due now or overdue
            // This prevents future occurrences from showing prematurely
            if (dueDate > now) {
              return false;
            }
          }

          return true;
        });
      }
    }

    // Sort the tasks
    return filteredTasks.sort((a, b) => {
      const { field, direction } = sortConfig;
      const multiplier = direction === 'asc' ? 1 : -1;

      switch (field) {
        case 'dueDate':
          // Handle 'No due date' special case
          if (a.dueDate === 'No due date' && b.dueDate !== 'No due date') return multiplier;
          if (a.dueDate !== 'No due date' && b.dueDate === 'No due date') return -multiplier;
          if (a.dueDate === 'No due date' && b.dueDate === 'No due date') return 0;
          return multiplier * (new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime());

        case 'severity':
          // Map severity to numeric values for sorting
          const severityMap: Record<MaintenanceSeverity, number> = {
            critical: 4,
            high: 3,
            medium: 2,
            low: 1
          };
          return multiplier * (severityMap[a.severity] - severityMap[b.severity]);

        case 'createdAt':
          return multiplier * (new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());

        case 'status':
          // Map status to numeric values for sorting
          const statusMap: Record<string, number> = {
            new: 1,
            assigned: 2,
            accepted: 3,
            in_progress: 4,
            completed: 5,
            cancelled: 6
          };
          return multiplier * ((statusMap[a.status] || 0) - (statusMap[b.status] || 0));

        default:
          return 0;
      }
    });
  }, [tasks, searchQuery, statusFilter, severityFilter, providerFilter, propertyFilter, sortConfig, showCompleted, showRecurring, providers]);

  return (
    <Tabs defaultValue="tasks" className="w-full" onValueChange={setActiveTab} value={activeTab}>
      <TabsList className="grid w-full grid-cols-2 mb-3 h-9 sticky top-0 z-10 bg-background/80 backdrop-blur-sm">
        <TabsTrigger
          value="tasks"
          className="text-xs sm:text-sm py-1 relative group h-7"
        >
          <span>Tasks</span>
          <span className="hidden sm:inline ml-1">& Maintenance</span>
        </TabsTrigger>
        <TabsTrigger
          value="providers"
          className="text-xs sm:text-sm py-1 relative group h-7"
        >
          <span>Providers</span>
          <span className="hidden sm:inline ml-1">& Services</span>
        </TabsTrigger>
      </TabsList>

      <TabsContent value="tasks" className="mt-0">
        {activeTab === "tasks" && (
          <>
            {/* Search Bar and Action Buttons - Inline */}
            <div className="mb-3 space-y-3">
              <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 sm:items-center">
                <div className="flex-1 min-w-0">
                  <SearchBar
                    value={searchQuery}
                    onChange={setSearchQuery}
                    placeholder="Search maintenance tasks..."
                    className="w-full"
                  />
                </div>
                {actionButtons && (
                  <div className="flex-shrink-0">
                    {actionButtons}
                  </div>
                )}
              </div>

              {/* Status Filters - More compact */}
              <div className="overflow-x-auto pb-1 -mx-2 px-2 max-w-full">
                <div className="flex space-x-1.5 min-w-max">
                  {filterOptions.map((option) => (
                    <button
                      key={option.label}
                      onClick={() => setStatusFilter(option.value)}
                      className={`px-2.5 py-1 text-xs rounded-md transition-colors whitespace-nowrap ${
                        statusFilter === option.value
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
                      }`}
                    >
                      {option.label}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Compact Action Bar */}
            <div className="mb-3 flex flex-wrap gap-1.5 text-xs">
              {/* Print Button */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsPrintDialogOpen(true)}
                className="h-8 px-2 text-xs bg-green-50 border-green-200 hover:bg-green-100 dark:bg-green-950/20 dark:border-green-800/30 dark:hover:bg-green-900/30"
              >
                <Printer className="h-3 w-3 mr-1 text-green-600 dark:text-green-400" />
                <span className="hidden sm:inline">Print</span>
              </Button>

              {/* Show/Hide Completed Toggle */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowCompleted(!showCompleted)}
                className={`h-8 px-2 text-xs ${showCompleted ? 'bg-blue-50 border-blue-200 dark:bg-blue-950/20 dark:border-blue-800/30' : ''}`}
              >
                <span className="hidden sm:inline">{showCompleted ? 'Hide Completed' : 'Show Completed'}</span>
                <span className="sm:hidden">{showCompleted ? 'Hide Done' : 'Show Done'}</span>
              </Button>

              {/* Show/Hide Recurring Toggle */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowRecurring(!showRecurring)}
                className={`h-8 px-2 text-xs ${showRecurring ? 'bg-purple-50 border-purple-200 dark:bg-purple-950/20 dark:border-purple-800/30' : ''}`}
              >
                <Repeat className="h-3 w-3 mr-1" />
                <span className="hidden sm:inline">{showRecurring ? 'Hide Recurring' : 'Show Recurring'}</span>
                <span className="sm:hidden">Recurring</span>
              </Button>

              {/* Priority Filter */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-8 px-2 text-xs">
                    <AlertTriangle className="h-3 w-3 mr-1" />
                    <span className="hidden sm:inline">
                      {severityFilter ? `${severityFilter.charAt(0).toUpperCase() + severityFilter.slice(1)}` : 'Priority'}
                    </span>
                    <span className="sm:hidden">Priority</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {severityOptions.map(option => (
                    <DropdownMenuItem
                      key={option.label}
                      onClick={() => setSeverityFilter(option.value)}
                      className={severityFilter === option.value ? 'bg-muted' : ''}
                    >
                      {option.label}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Provider Filter */}
              {providers && providers.length > 0 && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="h-8 px-2 text-xs">
                      <span className="h-3 w-3 mr-1 flex items-center justify-center text-xs">
                        👤
                      </span>
                      <span className="hidden sm:inline">
                        {providerFilter ? (providers.find(p => p.id === providerFilter)?.name || 'Unknown') : 'Provider'}
                      </span>
                      <span className="sm:hidden">Provider</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      onClick={() => setProviderFilter(undefined)}
                      className={providerFilter === undefined ? 'bg-muted' : ''}
                    >
                      All Providers
                    </DropdownMenuItem>

                    {providers.map(provider => (
                      <DropdownMenuItem
                        key={provider.id}
                        onClick={() => setProviderFilter(provider.id)}
                        className={providerFilter === provider.id ? 'bg-muted' : ''}
                      >
                        {provider.name}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}

              {/* Property Filter */}
              {properties.length > 0 && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="h-8 px-2 text-xs">
                      <span className="h-3 w-3 mr-1 flex items-center justify-center text-xs">
                        🏠
                      </span>
                      <span className="hidden sm:inline">
                        {propertyFilter ? (properties.find(p => p.id === propertyFilter)?.name || 'Unknown') : 'Property'}
                      </span>
                      <span className="sm:hidden">Property</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      onClick={() => setPropertyFilter(undefined)}
                      className={propertyFilter === undefined ? 'bg-muted' : ''}
                    >
                      All Properties
                    </DropdownMenuItem>

                    {properties.map(property => (
                      <DropdownMenuItem
                        key={property.id}
                        onClick={() => setPropertyFilter(property.id)}
                        className={propertyFilter === property.id ? 'bg-muted' : ''}
                      >
                        {property.name}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}

              {/* Sort Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-8 px-2 text-xs">
                    <ArrowUpDown className="h-3 w-3 mr-1" />
                    <span className="hidden sm:inline">{currentSortLabel}</span>
                    <span className="sm:hidden">Sort</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {sortOptions.map(option => (
                    <DropdownMenuItem
                      key={`${option.field}-${option.direction}`}
                      onClick={() => handleSort(option.field, option.direction)}
                      className={sortConfig.field === option.field && sortConfig.direction === option.direction ? 'bg-muted' : ''}
                    >
                      <div className="flex items-center">
                        {option.icon}
                        {option.label}
                      </div>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <MaintenanceList
              tasks={processedTasks}
              loading={loading}
              onTaskClick={onTaskClick}
              onStatusChange={onStatusChange}
              onDeleteTask={onDeleteTask}
              filter={statusFilter}
            />

            {/* Print Dialog */}
            <PrintDialog
              open={isPrintDialogOpen}
              onOpenChange={setIsPrintDialogOpen}
              tasks={tasks} // Use all tasks instead of processedTasks to ensure all tasks are available for export
              filters={{
                status: statusFilter,
                severity: severityFilter,
                provider: providerFilter,
                property: propertyFilter,
                showCompleted,
                sortField: sortConfig.field,
                sortDirection: sortConfig.direction
              }}
              providers={providers}
              properties={properties}
            />
          </>
        )}
      </TabsContent>

      <TabsContent value="providers" className="mt-0">
        {activeTab === "providers" && onAddProvider && onUpdateProvider && onDeleteProvider && (
          <ProviderManagement
            providers={providers || []}
            onAddProvider={onAddProvider}
            onUpdateProvider={onUpdateProvider}
            onDeleteProvider={onDeleteProvider}
          />
        )}
      </TabsContent>
    </Tabs>
  );
};

export default MaintenanceTabs;
