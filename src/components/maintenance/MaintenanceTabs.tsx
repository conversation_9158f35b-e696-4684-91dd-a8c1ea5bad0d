
import React, { useState, useMemo } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import MaintenanceList from './MaintenanceList';
import ProviderManagement from './ProviderManagement';
import { MaintenanceTask, MaintenanceStatus, MaintenanceSeverity } from './types';
import { Provider } from './types';
import Tooltip from '@/components/ui/Tooltip';
import { Button } from '@/components/ui/button';
import { ArrowUpDown, ArrowDown, ArrowUp, Calendar, Clock, AlertTriangle, Printer, Repeat } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import PrintDialog from './PrintDialog';
import { SearchBar } from '@/components/ui/FilterStandardization';

interface MaintenanceTabsProps {
  tasks: MaintenanceTask[];
  providers?: Provider[];
  loading?: boolean;
  onTaskClick?: (task: MaintenanceTask) => void;
  onAddProvider?: (provider: Omit<Provider, 'id'>) => void;
  onUpdateProvider?: (id: string, provider: Omit<Provider, 'id'>) => void;
  onDeleteProvider?: (id: string) => void;
  onStatusChange?: (taskId: string, newStatus: MaintenanceStatus) => Promise<boolean>;
  onDeleteTask?: (taskId: string) => Promise<boolean>;
}

// Sort types
type SortField = 'dueDate' | 'severity' | 'createdAt' | 'status';
type SortDirection = 'asc' | 'desc';

interface SortOption {
  field: SortField;
  direction: SortDirection;
  label: string;
  icon: React.ReactNode;
}

const MaintenanceTabs: React.FC<MaintenanceTabsProps> = ({
  tasks,
  providers = [],
  loading,
  onTaskClick,
  onAddProvider,
  onUpdateProvider,
  onDeleteProvider,
  onStatusChange,
  onDeleteTask
}) => {
  const [activeTab, setActiveTab] = useState("tasks");
  const [isPrintDialogOpen, setIsPrintDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | undefined>(() => {
    // Check for URL parameter in session storage
    const savedFilter = window.sessionStorage.getItem('maintenance_status_filter');
    if (savedFilter) {
      // Clear it after reading
      window.sessionStorage.removeItem('maintenance_status_filter');
      return savedFilter;
    }
    return undefined; // Default to "All Tasks"
  });

  const [severityFilter, setSeverityFilter] = useState<MaintenanceSeverity | undefined>(() => {
    // Check for URL parameter in session storage
    const savedFilter = window.sessionStorage.getItem('maintenance_severity_filter') as MaintenanceSeverity | null;
    if (savedFilter) {
      // Clear it after reading
      window.sessionStorage.removeItem('maintenance_severity_filter');
      return savedFilter;
    }
    return undefined; // Default
  });

  const [providerFilter, setProviderFilter] = useState<string | undefined>(() => {
    // Check for URL parameter in session storage
    const savedFilter = window.sessionStorage.getItem('maintenance_provider_filter');
    if (savedFilter) {
      // Clear it after reading
      window.sessionStorage.removeItem('maintenance_provider_filter');
      return savedFilter;
    }
    return undefined; // Default
  });

  const [propertyFilter, setPropertyFilter] = useState<string | undefined>(() => {
    // Check for URL parameter in session storage
    const savedFilter = window.sessionStorage.getItem('maintenance_property_filter');
    if (savedFilter) {
      // Clear it after reading
      window.sessionStorage.removeItem('maintenance_property_filter');
      return savedFilter;
    }
    return undefined; // Default
  });

  const [sortConfig, setSortConfig] = useState<{field: SortField, direction: SortDirection}>({field: 'dueDate', direction: 'asc'});
  const [showCompleted, setShowCompleted] = useState(() => {
    // Check for URL parameter in session storage
    const savedSetting = window.sessionStorage.getItem('maintenance_show_completed');
    if (savedSetting) {
      // Clear it after reading
      window.sessionStorage.removeItem('maintenance_show_completed');
      return savedSetting === 'true';
    }
    return false; // Default
  });

  const [showRecurring, setShowRecurring] = useState(false);

  // Get unique properties from tasks for property filter
  const properties = useMemo(() => {
    const uniqueProperties = new Map<string, {id: string, name: string}>();

    tasks.forEach(task => {
      if (task.propertyId && task.propertyName) {
        uniqueProperties.set(task.propertyId, {
          id: task.propertyId,
          name: task.propertyName
        });
      }
    });

    return Array.from(uniqueProperties.values());
  }, [tasks]);

  // Status filter options
  const filterOptions = [
    { value: undefined, label: 'All Tasks', tooltip: 'View all maintenance tasks regardless of status' },
    { value: 'new', label: 'New', tooltip: 'Tasks that have been created but not yet assigned' },
    { value: 'assigned', label: 'Assigned', tooltip: 'Tasks that have been assigned to a service provider' },
    { value: 'accepted', label: 'Accepted', tooltip: 'Tasks that have been accepted by a service provider' },
    { value: 'in_progress', label: 'In Progress', tooltip: 'Tasks currently being worked on' },
    { value: 'completed', label: 'Completed', tooltip: 'Tasks that have been finished' }
  ];

  // Severity filter options
  const severityOptions: { value: MaintenanceSeverity | undefined; label: string; tooltip: string }[] = [
    { value: undefined, label: 'All Priorities', tooltip: 'View tasks of all priority levels' },
    { value: 'critical', label: 'Critical', tooltip: 'Urgent tasks that need immediate attention' },
    { value: 'high', label: 'High', tooltip: 'Important tasks that should be addressed soon' },
    { value: 'medium', label: 'Medium', tooltip: 'Standard priority tasks' },
    { value: 'low', label: 'Low', tooltip: 'Tasks that can be addressed when time permits' }
  ];

  // Sort options
  const sortOptions: SortOption[] = [
    { field: 'dueDate', direction: 'asc', label: 'Due Date (Earliest)', icon: <Calendar className="h-4 w-4 mr-2" /> },
    { field: 'dueDate', direction: 'desc', label: 'Due Date (Latest)', icon: <Calendar className="h-4 w-4 mr-2" /> },
    { field: 'severity', direction: 'desc', label: 'Priority (Highest)', icon: <AlertTriangle className="h-4 w-4 mr-2" /> },
    { field: 'severity', direction: 'asc', label: 'Priority (Lowest)', icon: <AlertTriangle className="h-4 w-4 mr-2" /> },
    { field: 'createdAt', direction: 'desc', label: 'Newest First', icon: <Clock className="h-4 w-4 mr-2" /> },
    { field: 'createdAt', direction: 'asc', label: 'Oldest First', icon: <Clock className="h-4 w-4 mr-2" /> },
  ];

  // Handle sorting
  const handleSort = (field: SortField, direction: SortDirection) => {
    setSortConfig({ field, direction });
  };

  // Get current sort option label
  const currentSortLabel = useMemo(() => {
    const option = sortOptions.find(opt =>
      opt.field === sortConfig.field && opt.direction === sortConfig.direction
    );
    return option ? option.label : 'Sort';
  }, [sortConfig, sortOptions]);

  // Filter and sort tasks
  const processedTasks = useMemo(() => {
    // Start with all tasks
    let filteredTasks = [...tasks];

    // Filter by search query if provided
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filteredTasks = filteredTasks.filter(task => {
        // Search in title, description, property name, provider name, and assigned to
        const searchableFields = [
          task.title,
          task.description,
          task.propertyName,
          task.assignedTo || '',
          // Find provider name from providers array
          providers.find(p => p.id === task.providerId)?.name || ''
        ].filter(Boolean); // Remove empty/undefined values
        
        return searchableFields.some(field => 
          field.toLowerCase().includes(query)
        );
      });
    }

    // Filter by status if a status filter is selected
    if (statusFilter) {
      filteredTasks = filteredTasks.filter(task => task.status === statusFilter);
    }

    // Filter by severity if a severity filter is selected
    if (severityFilter) {
      filteredTasks = filteredTasks.filter(task => task.severity === severityFilter);
    }

    // Filter by provider if a provider filter is selected
    if (providerFilter) {
      filteredTasks = filteredTasks.filter(task => task.providerId === providerFilter);
    }

    // Filter by property if a property filter is selected
    if (propertyFilter) {
      filteredTasks = filteredTasks.filter(task => task.propertyId === propertyFilter);
    }

    // Handle recurring task filtering
    if (showRecurring) {
      // Show only recurring tasks (both active and completed)
      filteredTasks = filteredTasks.filter(task => task.isRecurring);
    } else {
      // Hide completed recurring tasks unless explicitly showing completed tasks
      if (!showCompleted && statusFilter !== 'completed') {
        filteredTasks = filteredTasks.filter(task => {
          // Hide completed tasks
          if (task.status === 'completed' || task.status === 'cancelled') {
            // But allow completed non-recurring tasks to show if showCompleted is true
            return false;
          }
          return true;
        });
      }
    }

    // Sort the tasks
    return filteredTasks.sort((a, b) => {
      const { field, direction } = sortConfig;
      const multiplier = direction === 'asc' ? 1 : -1;

      switch (field) {
        case 'dueDate':
          // Handle 'No due date' special case
          if (a.dueDate === 'No due date' && b.dueDate !== 'No due date') return multiplier;
          if (a.dueDate !== 'No due date' && b.dueDate === 'No due date') return -multiplier;
          if (a.dueDate === 'No due date' && b.dueDate === 'No due date') return 0;
          return multiplier * (new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime());

        case 'severity':
          // Map severity to numeric values for sorting
          const severityMap: Record<MaintenanceSeverity, number> = {
            critical: 4,
            high: 3,
            medium: 2,
            low: 1
          };
          return multiplier * (severityMap[a.severity] - severityMap[b.severity]);

        case 'createdAt':
          return multiplier * (new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());

        case 'status':
          // Map status to numeric values for sorting
          const statusMap: Record<string, number> = {
            new: 1,
            assigned: 2,
            accepted: 3,
            in_progress: 4,
            completed: 5,
            cancelled: 6
          };
          return multiplier * ((statusMap[a.status] || 0) - (statusMap[b.status] || 0));

        default:
          return 0;
      }
    });
  }, [tasks, searchQuery, statusFilter, severityFilter, providerFilter, propertyFilter, sortConfig, showCompleted, showRecurring, providers]);

  return (
    <Tabs defaultValue="tasks" className="w-full" onValueChange={setActiveTab} value={activeTab}>
      <TabsList className="grid w-full grid-cols-2 mb-2 sm:mb-4 sticky top-0 z-10 bg-background/80 backdrop-blur-sm">
        <TabsTrigger
          value="tasks"
          className="text-sm sm:text-base py-1.5 sm:py-2 relative group"
        >
          <span>Maintenance Tasks</span>
          <Tooltip text="View and manage maintenance tasks for your properties" position="bottom">
            <span className="ml-1 text-muted-foreground cursor-help"></span>
          </Tooltip>
        </TabsTrigger>
        <TabsTrigger
          value="providers"
          className="text-sm sm:text-base py-1.5 sm:py-2 relative group"
        >
          <span>Service Providers</span>
          <Tooltip text="Manage your list of service providers (plumbers, cleaners, etc.)" position="bottom">
            <span className="ml-1 text-muted-foreground cursor-help"></span>
          </Tooltip>
        </TabsTrigger>
      </TabsList>

      <TabsContent value="tasks" className="mt-0">
        {activeTab === "tasks" && (
          <>
            {/* Search Bar */}
            <div className="mb-4">
              <SearchBar
                value={searchQuery}
                onChange={setSearchQuery}
                placeholder="Search maintenance tasks..."
                className="max-w-md"
              />
            </div>

            {/* Status Filters */}
            <div className="mb-3 overflow-x-auto pb-1 -mx-2 px-2 max-w-full">
              <div className="flex space-x-2 min-w-max">
                {filterOptions.map((option) => (
                  <div key={option.label} className="relative">
                    <button
                      onClick={() => setStatusFilter(option.value)}
                      className={`px-3 py-1.5 text-xs sm:text-sm rounded-full transition-colors whitespace-nowrap flex items-center gap-1 ${
                        statusFilter === option.value ? 'bg-primary text-primary-foreground' : 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
                      }`}
                    >
                      {option.label}
                    </button>
                    <Tooltip text={option.tooltip} position="bottom" className="z-20">
                      <span className="absolute top-0 right-0 h-2 w-2"></span>
                    </Tooltip>
                  </div>
                ))}
              </div>
            </div>

            {/* Sort and Additional Filters */}
            <div className="mb-4 flex flex-wrap gap-2">
              {/* Print Button */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsPrintDialogOpen(true)}
                className="bg-green-50 border-green-200 hover:bg-green-100 dark:bg-green-950/20 dark:border-green-800/30 dark:hover:bg-green-900/30"
              >
                <Printer className="h-4 w-4 mr-2 text-green-600 dark:text-green-400" />
                Print / Export
              </Button>

              {/* Show/Hide Completed Toggle */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowCompleted(!showCompleted)}
                className={showCompleted ? 'bg-blue-50 border-blue-200 dark:bg-blue-950/20 dark:border-blue-800/30' : ''}
              >
                {showCompleted ? 'Hide Completed/Cancelled' : 'Show Completed/Cancelled'}
              </Button>

              {/* Show/Hide Recurring Toggle */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowRecurring(!showRecurring)}
                className={showRecurring ? 'bg-purple-50 border-purple-200 dark:bg-purple-950/20 dark:border-purple-800/30' : ''}
              >
                <Repeat className="h-4 w-4 mr-1" />
                {showRecurring ? 'Hide Recurring' : 'Show Recurring'}
              </Button>

              {/* Priority Filter */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <AlertTriangle className="h-4 w-4 mr-2" />
                    {severityFilter ? `Priority: ${severityFilter.charAt(0).toUpperCase() + severityFilter.slice(1)}` : 'All Priorities'}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {severityOptions.map(option => (
                    <DropdownMenuItem
                      key={option.label}
                      onClick={() => setSeverityFilter(option.value)}
                      className={severityFilter === option.value ? 'bg-muted' : ''}
                    >
                      {option.label}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Provider Filter */}
              {providers && providers.length > 0 && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm">
                      <span className="h-4 w-4 mr-2 flex items-center justify-center">
                        👤
                      </span>
                      {providerFilter
                        ? `Provider: ${providers.find(p => p.id === providerFilter)?.name || 'Unknown'}`
                        : 'All Providers'}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      onClick={() => setProviderFilter(undefined)}
                      className={providerFilter === undefined ? 'bg-muted' : ''}
                    >
                      All Providers
                    </DropdownMenuItem>

                    {providers.map(provider => (
                      <DropdownMenuItem
                        key={provider.id}
                        onClick={() => setProviderFilter(provider.id)}
                        className={providerFilter === provider.id ? 'bg-muted' : ''}
                      >
                        {provider.name}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}

              {/* Property Filter */}
              {properties.length > 0 && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm">
                      <span className="h-4 w-4 mr-2 flex items-center justify-center">
                        🏠
                      </span>
                      {propertyFilter
                        ? `Property: ${properties.find(p => p.id === propertyFilter)?.name || 'Unknown'}`
                        : 'All Properties'}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      onClick={() => setPropertyFilter(undefined)}
                      className={propertyFilter === undefined ? 'bg-muted' : ''}
                    >
                      All Properties
                    </DropdownMenuItem>

                    {properties.map(property => (
                      <DropdownMenuItem
                        key={property.id}
                        onClick={() => setPropertyFilter(property.id)}
                        className={propertyFilter === property.id ? 'bg-muted' : ''}
                      >
                        {property.name}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}

              {/* Sort Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <ArrowUpDown className="h-4 w-4 mr-2" />
                    {currentSortLabel}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {sortOptions.map(option => (
                    <DropdownMenuItem
                      key={`${option.field}-${option.direction}`}
                      onClick={() => handleSort(option.field, option.direction)}
                      className={sortConfig.field === option.field && sortConfig.direction === option.direction ? 'bg-muted' : ''}
                    >
                      <div className="flex items-center">
                        {option.icon}
                        {option.label}
                      </div>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <MaintenanceList
              tasks={processedTasks}
              loading={loading}
              onTaskClick={onTaskClick}
              onStatusChange={onStatusChange}
              onDeleteTask={onDeleteTask}
              filter={statusFilter}
            />

            {/* Print Dialog */}
            <PrintDialog
              open={isPrintDialogOpen}
              onOpenChange={setIsPrintDialogOpen}
              tasks={tasks} // Use all tasks instead of processedTasks to ensure all tasks are available for export
              filters={{
                status: statusFilter,
                severity: severityFilter,
                provider: providerFilter,
                property: propertyFilter,
                showCompleted,
                sortField: sortConfig.field,
                sortDirection: sortConfig.direction
              }}
              providers={providers}
              properties={properties}
            />
          </>
        )}
      </TabsContent>

      <TabsContent value="providers" className="mt-0">
        {activeTab === "providers" && onAddProvider && onUpdateProvider && onDeleteProvider && (
          <ProviderManagement
            providers={providers || []}
            onAddProvider={onAddProvider}
            onUpdateProvider={onUpdateProvider}
            onDeleteProvider={onDeleteProvider}
          />
        )}
      </TabsContent>
    </Tabs>
  );
};

export default MaintenanceTabs;
