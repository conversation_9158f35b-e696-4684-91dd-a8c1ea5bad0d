
import React, { useState } from 'react';
import { MaintenanceTask, MaintenanceStatus } from './types';
import { Card, CardContent } from '@/components/ui/card';
import GlassCard from '@/components/ui/GlassCard';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { MoreHorizontal, CheckCircle, XCircle, AlertTriangle, Clock, CircleSlash, ThumbsUp, ThumbsDown } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';

interface MaintenanceCardProps {
  task: MaintenanceTask;
  onClick?: () => void;
  onStatusChange?: (taskId: string, newStatus: MaintenanceStatus) => Promise<boolean>;
  onDeleteTask?: (taskId: string) => Promise<boolean>;
}

const MaintenanceCard: React.FC<MaintenanceCardProps> = ({
  task,
  onClick,
  onStatusChange,
  onDeleteTask
}) => {
  const [isUpdating, setIsUpdating] = useState(false);
  const { authState } = useAuth();

  const handleStatusChange = async (newStatus: MaintenanceStatus) => {
    if (!onStatusChange) {
      console.error('onStatusChange handler not provided');
      toast.error('Status change handler not available');
      return;
    }

    console.log(`[MaintenanceCard] Attempting to change status from ${task.status} to ${newStatus} for task ${task.id}`);
    setIsUpdating(true);
    try {
      const success = await onStatusChange(task.id, newStatus);
      if (success) {
        console.log(`[MaintenanceCard] Successfully updated task ${task.id} status to ${newStatus}`);
        toast.success(`Task marked as ${newStatus}`);
      } else {
        console.error(`[MaintenanceCard] Failed to update task ${task.id} status to ${newStatus}`);
        toast.error('Failed to update task status - operation returned false');
      }
    } catch (error) {
      console.error('[MaintenanceCard] Exception updating task status:', error);
      toast.error(`Failed to update task status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDelete = async () => {
    if (!onDeleteTask) {
      console.error('onDeleteTask handler not provided');
      toast.error('Delete handler not available');
      return;
    }

    if (window.confirm('Are you sure you want to delete this task?')) {
      console.log(`[MaintenanceCard] Attempting to delete task ${task.id}`);
      setIsUpdating(true);
      try {
        const success = await onDeleteTask(task.id);
        if (success) {
          console.log(`[MaintenanceCard] Successfully deleted task ${task.id}`);
          toast.success('Task deleted successfully');
        } else {
          console.error(`[MaintenanceCard] Failed to delete task ${task.id}`);
          toast.error('Failed to delete task - operation returned false');
        }
      } catch (error) {
        console.error('[MaintenanceCard] Exception deleting task:', error);
        toast.error(`Failed to delete task: ${error instanceof Error ? error.message : 'Unknown error'}`);
      } finally {
        setIsUpdating(false);
      }
    }
  };

  const getSeverityIcon = () => {
    switch (task.severity) {
      case 'low':
        return <Clock className="h-5 w-5 text-green-500" />;
      case 'medium':
        return <AlertTriangle className="h-5 w-5 text-amber-500" />;
      case 'high':
        return <AlertTriangle className="h-5 w-5 text-orange-500" />;
      case 'critical':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadgeColor = () => {
    switch (task.status) {
      case 'new': return 'bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white';
      case 'assigned': return 'bg-amber-500 hover:bg-amber-600 dark:bg-amber-600 dark:hover:bg-amber-700 text-white';
      case 'in_progress': return 'bg-purple-500 hover:bg-purple-600 dark:bg-purple-600 dark:hover:bg-purple-700 text-white';
      case 'completed': return 'bg-green-500 hover:bg-green-600 dark:bg-green-600 dark:hover:bg-green-700 text-white';
      case 'cancelled': return 'bg-gray-500 hover:bg-gray-600 dark:bg-gray-600 dark:hover:bg-gray-700 text-white';
      case 'accepted': return 'bg-emerald-500 hover:bg-emerald-600 dark:bg-emerald-600 dark:hover:bg-emerald-700 text-white';
      case 'rejected': return 'bg-red-500 hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700 text-white';
      default: return 'bg-gray-500 hover:bg-gray-600 dark:bg-gray-600 dark:hover:bg-gray-700 text-white';
    }
  };

  return (
    <GlassCard 
      variant="interactive" 
      colorScheme="amber" 
      hoverEffect 
      onClick={onClick}
      className="cursor-pointer"
    >
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <div className="mt-1">
              {getSeverityIcon()}
            </div>
            <div>
              <h3 className="font-medium text-gray-900 dark:text-gray-100">{task.title}</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 line-clamp-2">{task.description}</p>

              <div className="flex flex-wrap items-center mt-2 gap-2">
                <Badge className={getStatusBadgeColor()}>
                  {task.status}
                </Badge>

                {task.propertyName && (
                  <span className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 px-2 py-1 rounded">
                    {task.propertyName}
                  </span>
                )}

                {task.dueDate && task.dueDate !== 'No due date' && (
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    Due: {task.dueDate}
                  </span>
                )}
              </div>
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
              <Button variant="ghost" size="sm" disabled={isUpdating}>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {/* Status options available to all users */}
              <DropdownMenuItem onClick={(e) => { e.stopPropagation(); handleStatusChange('completed'); }}>
                <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                Mark as Completed
              </DropdownMenuItem>
              <DropdownMenuItem onClick={(e) => { e.stopPropagation(); handleStatusChange('in_progress'); }}>
                <Clock className="h-4 w-4 mr-2 text-purple-500" />
                Mark as In Progress
              </DropdownMenuItem>

              {/* Options only available to non-service providers */}
              {authState?.profile?.role !== 'service_provider' && (
                <>
                  <DropdownMenuItem onClick={(e) => { e.stopPropagation(); handleStatusChange('cancelled'); }}>
                    <CircleSlash className="h-4 w-4 mr-2 text-gray-500" />
                    Cancel Task
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDelete();
                    }}
                    className="text-red-500 focus:text-red-500"
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
    </GlassCard>
  );
};

export default MaintenanceCard;
