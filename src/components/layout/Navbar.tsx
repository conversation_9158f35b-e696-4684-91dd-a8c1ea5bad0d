
import React from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import {
  Settings,
  LogOut,
  User,
  Menu,
  AlertCircle,
  ChevronLeft,
  Search
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useImpersonation } from '@/contexts/ImpersonationContext';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { checkForUpdates } from '@/utils/serviceWorkerRegistration';
import PermissionBasedNavigation from './PermissionBasedNavigation';

interface NavbarProps {
  toggleSidebar: () => void;
}

const Navbar: React.FC<NavbarProps> = ({ toggleSidebar }) => {
  const { authState, signOut, stopImpersonation } = useAuth();
  const { isImpersonating, impersonatedUserName, impersonatedUserEmail } = useImpersonation();
  const navigate = useNavigate();
  const location = useLocation();

  // Improved signOut handler with error handling and feedback
  const handleSignOut = async (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault(); // Prevent any default behavior
      e.stopPropagation(); // Stop event propagation to prevent interference with dropdown
    }

    try {
      console.log('Navbar: Starting sign out process');
      // Show loading toast while signing out
      const toastId = toast.loading('Signing out...');

      // Clear any cached data from localStorage
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('cache:')) {
          localStorage.removeItem(key);
        }
      });

      // Clear sessionStorage
      sessionStorage.clear();

      // Check for service worker updates before signing out
      if ('serviceWorker' in navigator) {
        checkForUpdates();
      }

      console.log('Navbar: Calling signOut function');
      // Wait for signout to complete
      await signOut();

      // Clear any loading toast and show success message
      toast.dismiss(toastId);
      toast.success('Successfully logged out');

      console.log('Navbar: Sign out completed, redirecting to login');
      // Use a short timeout to ensure the state changes are complete
      // before navigating away
      setTimeout(() => {
        // Force reload the page after logout to clear any cached state
        // Use hash routing for consistent navigation
        window.location.href = '/#/login';
      }, 300);
    } catch (error) {
      console.error('Error during sign out:', error);
      toast.error('Failed to sign out. Please try again.');

      // Even on error, try to redirect to login
      setTimeout(() => {
        window.location.href = '/#/login';
      }, 1000);
    }
  };

  const handleStopImpersonation = async () => {
    try {
      console.log('Navbar: Stopping impersonation');
      if (!isImpersonating) {
        console.warn('No active impersonation session to stop');
        toast.error('No active impersonation session');
        return;
      }

      const toastId = toast.loading('Ending impersonation...');
      await stopImpersonation();
      toast.dismiss(toastId);
      toast.success('Returned to your account');
      navigate('/admin');
    } catch (error) {
      console.error('Error stopping impersonation:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to end impersonation: ${errorMessage}`);

      // Force reload as a last resort
      setTimeout(() => {
        window.location.href = '/admin';
      }, 1000);
    }
  };

  const userInitials =
    authState?.profile?.first_name && authState?.profile?.last_name
      ? `${authState?.profile.first_name[0]}${authState?.profile.last_name[0]}`
      : authState.user?.email?.substring(0, 2) || 'U';

  // Navigation items are now handled by PermissionBasedNavigation component

  // If we're not on the home page, show a back button on mobile
  const showBackButton = location.pathname !== '/' && location.pathname !== '/dashboard';

  // Get the current page title from the path
  const getPageTitle = () => {
    const path = location.pathname.split('/')[1];
    if (!path) return 'Dashboard';
    return path.charAt(0).toUpperCase() + path.slice(1);
  };

  return (
    <header className="sticky top-0 z-10 w-full border-b bg-background/95 backdrop-blur-sm">
      <div className="flex h-12 items-center justify-between px-2">
        <div className="flex items-center justify-between w-full md:w-auto">
          <div className="flex items-center">
            {showBackButton ? (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate(-1)}
                className="mr-2"
                aria-label="Go back"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
            ) : (
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleSidebar}
                className="mr-2"
                id="sidebar-toggle"
                aria-label="Toggle menu"
              >
                <Menu className="h-4 w-4" />
              </Button>
            )}

            {/* Page title */}
            <div className="font-medium text-base">{getPageTitle()}</div>
          </div>

          <div className="flex items-center gap-2">
            {/* Search button - always visible on mobile */}
            <Button variant="ghost" size="sm" aria-label="Search">
              <Search className="h-4 w-4" />
            </Button>

          {/* Show impersonation indicator when active */}
          {isImpersonating && (
            <Button
              variant="destructive"
              size="sm"
              onClick={handleStopImpersonation}
              className="flex items-center gap-1 text-xs py-1 h-8"
              title="Return to your admin account"
            >
              <AlertCircle size={12} />
              <span className="hidden sm:inline">Exit Impersonation</span>
              <span className="sm:hidden">Exit</span>
            </Button>
          )}

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-8 w-8 rounded-full p-0">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={authState?.profile?.avatar_url || ''} />
                  <AvatarFallback className="text-xs">{userInitials}</AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>
                <div>
                  {authState?.profile?.first_name} {authState?.profile?.last_name}
                  <p className="text-xs text-muted-foreground mt-1 truncate max-w-40">
                    {authState.user?.email}
                  </p>
                  {isImpersonating && (
                    <div className="mt-1 px-2 py-1 bg-destructive/10 text-destructive text-xs rounded">
                      Impersonating: {impersonatedUserName || impersonatedUserEmail || 'User'}
                    </div>
                  )}
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link to="/settings">
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Settings</span>
                </Link>
              </DropdownMenuItem>
              {authState?.profile?.is_super_admin && (
                <DropdownMenuItem asChild>
                  <Link to="/admin">
                    <User className="mr-2 h-4 w-4" />
                    <span>Admin</span>
                  </Link>
                </DropdownMenuItem>
              )}
              {isImpersonating && (
                <DropdownMenuItem onClick={handleStopImpersonation}>
                  <AlertCircle className="mr-2 h-4 w-4" />
                  <span>End Impersonation</span>
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              {/* Updated logout button for better reliability */}
              <DropdownMenuItem onClick={handleSignOut} className="cursor-pointer">
                <LogOut className="mr-2 h-4 w-4" />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
};

export default Navbar;
