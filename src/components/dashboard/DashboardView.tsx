import React, { memo, useMemo, useEffect, useState } from 'react';
import {
  Building2,
  Wrench,
  Package,
  AlertCircle,
  ArrowRight,
  Calendar,
  Circle,
  ShoppingCart,
  BarChart3,
  InfoIcon,
  PieChart,
  Activity,
  CalendarCheck,
  ListTodo,
  PackageSearch,
  ClipboardList,
  ChevronDown,
  ChevronRight,
  AlertTriangle
} from 'lucide-react';
import { Property } from '../properties/PropertyCard';
import { MaintenanceTask } from '../maintenance/types';
import { InventoryItem } from '../inventory/types';
import { cn } from '@/lib/utils';
import { useNavigate } from 'react-router-dom';
import { PurchaseOrder } from '@/types/inventory';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import StatCard from "@/components/ui/StatCard";

interface Damage {
  id: string;
  title: string;
  propertyName: string;
  status: 'new' | 'pending' | 'completed';
  reportedAt: string; // Assuming this is a formatted string like "Apr 15" or similar
}

interface DashboardViewProps {
  properties: Property[];
  maintenanceTasks: MaintenanceTask[];
  inventoryItems: InventoryItem[];
  damages: Damage[];
  purchaseOrders?: PurchaseOrder[];
  isLoading?: boolean;
  onViewMore: (section: 'properties' | 'maintenance' | 'inventory' | 'damages' | 'purchaseOrders') => void;
  onViewOrder?: (order: PurchaseOrder) => void;
}

// Helper function to calculate days until a date string (handles date ranges like "Apr 24 - Apr 27, 2025")
function calculateDaysUntil(dateStr: string | null | undefined): number {
  if (!dateStr) return Infinity;
  try {
    console.log(`[calculateDaysUntil] Calculating days until: ${dateStr}`);

    // Special case for YYYY-MM-DD format (next_checkin_date)
    if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
      console.log(`[calculateDaysUntil] Detected ISO date format: ${dateStr}`);

      // Create date objects using the date parts to avoid timezone issues
      const [year, month, day] = dateStr.split('-').map(Number);
      const targetDate = new Date(year, month - 1, day); // month is 0-indexed in JS Date

      // Create today's date at midnight in local timezone
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayYear = today.getFullYear();
      const todayMonth = today.getMonth();
      const todayDay = today.getDate();
      const todayNormalized = new Date(todayYear, todayMonth, todayDay);

      // Check if the date is valid
      if (isNaN(targetDate.getTime())) {
        console.error(`[calculateDaysUntil] Invalid ISO date: ${dateStr}`);
        return Infinity;
      }

      const diffTime = targetDate.getTime() - todayNormalized.getTime();
      console.log(`[calculateDaysUntil] Today: ${todayNormalized.toISOString()}, Target: ${targetDate.toISOString()}, Diff ms: ${diffTime}`);

      if (diffTime < 0) {
        console.log(`[calculateDaysUntil] Date ${dateStr} is in the past`);
        return Infinity; // Past dates are not "upcoming"
      }

      const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));
      console.log(`[calculateDaysUntil] Days until ${dateStr}: ${diffDays}`);
      return diffDays;
    }
    // Check if the date string is a range (contains a hyphen)
    else if (dateStr.includes('-')) {
      console.log(`[calculateDaysUntil] Detected date range: ${dateStr}`);

      // Extract the start date from the range (e.g., "Apr 24" from "Apr 24 - Apr 27, 2025")
      const parts = dateStr.split('-');
      let startDateStr = parts[0].trim();

      // If the year is only at the end of the range, add it to the start date
      if (!startDateStr.includes(',') && parts[1].includes(',')) {
        const year = parts[1].trim().split(',')[1].trim();
        startDateStr = `${startDateStr}, ${year}`;
      }

      console.log(`[calculateDaysUntil] Extracted start date: ${startDateStr}`);

      // Parse the start date
      const startDate = new Date(startDateStr);

      // Check if the date is valid
      if (isNaN(startDate.getTime())) {
        console.error(`[calculateDaysUntil] Invalid start date: ${startDateStr}`);

        // Try using next_checkin_date if available
        if (typeof window !== 'undefined') {
          const properties = JSON.parse(localStorage.getItem('properties') || '[]');
          const property = properties.find((p: any) => p.next_booking === dateStr);
          if (property && property.next_checkin_date) {
            console.log(`[calculateDaysUntil] Using next_checkin_date instead: ${property.next_checkin_date}`);
            return calculateDaysUntil(property.next_checkin_date);
          }
        }

        return Infinity;
      }

      // Create today's date at midnight in local timezone
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayYear = today.getFullYear();
      const todayMonth = today.getMonth();
      const todayDay = today.getDate();
      const todayNormalized = new Date(todayYear, todayMonth, todayDay);

      // Normalize the start date to midnight in local timezone
      const startYear = startDate.getFullYear();
      const startMonth = startDate.getMonth();
      const startDay = startDate.getDate();
      const startDateNormalized = new Date(startYear, startMonth, startDay);

      const diffTime = startDateNormalized.getTime() - todayNormalized.getTime();
      console.log(`[calculateDaysUntil] Today: ${todayNormalized.toISOString()}, Target: ${startDateNormalized.toISOString()}, Diff ms: ${diffTime}`);

      if (diffTime < 0) {
        console.log(`[calculateDaysUntil] Date ${startDateStr} is in the past`);
        return Infinity; // Past dates are not "upcoming"
      }

      const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));
      console.log(`[calculateDaysUntil] Days until ${startDateStr}: ${diffDays}`);
      return diffDays;
    } else {
      // Handle single date (not a range)
      // If the date string doesn't include time, add T00:00:00 to ensure consistent parsing
      const normalizedDateStr = dateStr.includes('T') ? dateStr : `${dateStr}T00:00:00`;
      const targetDate = new Date(normalizedDateStr);

      // Check if the date is valid
      if (isNaN(targetDate.getTime())) {
        console.error(`[calculateDaysUntil] Invalid date: ${dateStr}`);
        return Infinity;
      }

      // Create today's date at midnight in local timezone
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayYear = today.getFullYear();
      const todayMonth = today.getMonth();
      const todayDay = today.getDate();
      const todayNormalized = new Date(todayYear, todayMonth, todayDay);

      // Normalize the target date to midnight in local timezone
      const targetYear = targetDate.getFullYear();
      const targetMonth = targetDate.getMonth();
      const targetDay = targetDate.getDate();
      const targetDateNormalized = new Date(targetYear, targetMonth, targetDay);

      const diffTime = targetDateNormalized.getTime() - todayNormalized.getTime();
      console.log(`[calculateDaysUntil] Today: ${todayNormalized.toISOString()}, Target: ${targetDateNormalized.toISOString()}, Diff ms: ${diffTime}`);

      if (diffTime < 0) {
        console.log(`[calculateDaysUntil] Date ${dateStr} is in the past`);
        return Infinity; // Past dates are not "upcoming"
      }

      const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));
      console.log(`[calculateDaysUntil] Days until ${dateStr}: ${diffDays}`);
      return diffDays;
    }
  } catch (e) {
    console.error("Error parsing date:", dateStr, e);
    return Infinity;
  }
}

// Helper function to format date (handles date ranges)
const formatDate = (dateStr: string | null | undefined): string => {
  if (!dateStr) return 'N/A';
  try {
    console.log(`[formatDate] Formatting date: ${dateStr}`);

    // If it's a date range (contains a hyphen), return it as is
    if (dateStr.includes('-')) {
      console.log(`[formatDate] Date range detected, returning as is: ${dateStr}`);
      return dateStr;
    }

    // For single dates
    // If the date string doesn't include time, add T00:00:00 to ensure consistent parsing
    const normalizedDateStr = dateStr.includes('T') ? dateStr : `${dateStr}T00:00:00`;
    const date = new Date(normalizedDateStr);

    if (isNaN(date.getTime())) {
      console.error(`[formatDate] Invalid date: ${dateStr}`);
      // Return the original string if we can't parse it
      return dateStr;
    }

    const formatted = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
    console.log(`[formatDate] Formatted ${dateStr} as: ${formatted}`);
    return formatted;
  } catch (e) {
    console.error("Error formatting date:", dateStr, e);
    // Return the original string if we encounter an error
    return dateStr;
  }
};

 // Memoize the component to prevent unnecessary re-renders
 const DashboardView: React.FC<DashboardViewProps> = ({
  properties = [], // Default to empty array
  maintenanceTasks = [], // Default to empty array
  inventoryItems = [], // Default to empty array
  damages = [], // Default to empty array
  purchaseOrders = [],
  isLoading = false,
  onViewMore,
  onViewOrder,
}) => {
  const navigate = useNavigate();

  // Accordion state management
  const [expandedSections, setExpandedSections] = useState<{
    properties: boolean;
    maintenance: boolean;
    criticalTasks: boolean;
    lowStock: boolean;
    newDamages: boolean;
    pendingOrders: boolean;
    checkins: boolean;
  }>({
    properties: false,
    maintenance: false,
    criticalTasks: false,
    lowStock: false,
    newDamages: false,
    pendingOrders: false,
    checkins: false,
  });

  // Toggle accordion section
  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Log maintenance tasks when they change
  useEffect(() => {
    console.log('[DashboardView] Maintenance tasks received:', maintenanceTasks);
  }, [maintenanceTasks]);

  // Filter critical tasks
  const criticalTasks = useMemo(() => isLoading ? [] : maintenanceTasks.filter(
    (task) => task && (task.severity === 'critical' || task.severity === 'high')
  ), [maintenanceTasks, isLoading]);

  // Filter low stock items
  const lowStockItems = useMemo(() => isLoading ? [] : inventoryItems.filter(
    (item) => item && typeof item.quantity === 'number' && typeof item.minQuantity === 'number' && item.quantity <= item.minQuantity
  ), [inventoryItems, isLoading]);

  // Filter new damage reports
  const newDamages = useMemo(() => isLoading ? [] : damages.filter((damage) => damage && damage.status === 'new'), [damages, isLoading]);

  // Filter pending purchase orders
  const pendingOrders = useMemo(() => isLoading ? [] : purchaseOrders.filter(
    (order) => order && order.status === 'pending'
  ), [purchaseOrders, isLoading]);

  // Filter upcoming check-ins (within next 7 days, sorted)
  const upcomingCheckins = useMemo(() => {
    if (isLoading) return [];

    console.log('[DashboardView] Processing properties for check-ins:', properties.length);
    console.log('[DashboardView] All properties:', JSON.stringify(properties, null, 2));

    // Log all properties with next_booking or next_checkin_date for debugging
    properties.forEach(p => {
      if (p.next_booking) {
        console.log(`[DashboardView] Property ${p.name} has next_booking: ${p.next_booking}`);
      }
      if (p.next_checkin_date) {
        console.log(`[DashboardView] Property ${p.name} has next_checkin_date: ${p.next_checkin_date}`);
      }
      if (p.next_checkin_formatted) {
        console.log(`[DashboardView] Property ${p.name} has next_checkin_formatted: ${p.next_checkin_formatted}`);
      }
    });

    // Store properties in localStorage for debugging
    if (typeof window !== 'undefined') {
      localStorage.setItem('properties', JSON.stringify(properties));
    }

    // First, create a copy of the properties array to avoid mutation issues
    const propertiesWithDays = properties
      .filter(p => {
        // Include properties that have either next_booking or next_checkin_date
        const hasNextBooking = !!p.next_booking;
        const hasNextCheckinDate = !!p.next_checkin_date;
        console.log(`[DashboardView] Property ${p.name}: has next booking = ${hasNextBooking}, has next checkin date = ${hasNextCheckinDate}`);
        return p && (hasNextBooking || hasNextCheckinDate);
      })
      .map(p => {
        // Try to calculate days using next_checkin_date first if available, then fall back to next_booking
        let daysUntil;
        if (p.next_checkin_date) {
          daysUntil = calculateDaysUntil(p.next_checkin_date);
          console.log(`[DashboardView] Property ${p.name}: using next_checkin_date, days until: ${daysUntil}`);
        } else if (p.next_booking) {
          daysUntil = calculateDaysUntil(p.next_booking);
          console.log(`[DashboardView] Property ${p.name}: using next_booking, days until: ${daysUntil}`);
        } else {
          daysUntil = Infinity;
          console.log(`[DashboardView] Property ${p.name}: no date available, setting days until to Infinity`);
        }

        return { ...p, daysUntil };
      });

    console.log('[DashboardView] Properties with days calculated:', propertiesWithDays.map(p => ({ name: p.name, daysUntil: p.daysUntil })));

    const upcomingProperties = propertiesWithDays
      .filter(p => {
        const isUpcoming = p.daysUntil <= 7 && p.daysUntil !== Infinity;
        console.log(`[DashboardView] Property ${p.name}: is upcoming (≤7 days) = ${isUpcoming}, days until: ${p.daysUntil}`);
        return isUpcoming; // Show check-ins within the next 7 days
      })
      .sort((a, b) => a.daysUntil - b.daysUntil);

    console.log('[DashboardView] Final upcoming properties:', upcomingProperties.map(p => p.name));
    return upcomingProperties;
  }, [properties, isLoading]);


  // Calculate counts for stat cards
  const propertiesCount = isLoading ? '-' : properties.length;
  const criticalTasksCount = isLoading ? '-' : criticalTasks.length;
  const lowStockItemsCount = isLoading ? '-' : lowStockItems.length;
  const pendingOrdersCount = isLoading ? '-' : pendingOrders.length;

   // Calculate specific counts for Property Overview
   const occupiedCount = useMemo(() => isLoading ? '-' : properties.filter(p => p && p.is_occupied).length, [properties, isLoading]);
   const vacantCount = useMemo(() => isLoading ? '-' : properties.filter(p => p && !p.is_occupied).length, [properties, isLoading]);
   const nextCheckin = useMemo(() => isLoading || upcomingCheckins.length === 0 ? null : upcomingCheckins[0], [upcomingCheckins, isLoading]);


    // Event handlers
    const handlePropertyClick = (id: string) => navigate(`/properties/${id}`);
  const handleTaskClick = (id: string) => navigate(`/maintenance?id=${id}`);
  const handleInventoryClick = (id: string) => navigate(`/inventory?itemId=${id}`);
  const handleDamageClick = (id: string) => navigate(`/damages/${id}`);
  const handleOrderClick = (order: PurchaseOrder) => {
    if (onViewOrder) {
      onViewOrder(order);
    } else {
      navigate(`/purchase-orders/${order.id}`);
    }
  };

  // Stat cards configuration with enhanced glassmorphism design
  const statCards = useMemo(() => [
    {
      title: 'Properties',
      value: propertiesCount,
      icon: Building2,
      colorScheme: 'blue' as const,
      onClick: () => onViewMore('properties'),
      alert: false,
      subtitle: `${occupiedCount} occupied, ${vacantCount} vacant`
    },
    {
      title: 'Critical Tasks',
      value: criticalTasksCount,
      icon: Wrench,
      colorScheme: 'amber' as const,
      onClick: () => onViewMore('maintenance'),
      alert: typeof criticalTasksCount === 'number' && criticalTasksCount > 0,
      subtitle: 'Requires immediate attention'
    },
    {
      title: 'Low Stock Items',
      value: lowStockItemsCount,
      icon: Package,
      colorScheme: 'purple' as const,
      onClick: () => onViewMore('inventory'),
      alert: typeof lowStockItemsCount === 'number' && lowStockItemsCount > 0,
      subtitle: 'Need restocking'
    },
    {
      title: 'Pending Orders',
      value: pendingOrdersCount,
      icon: ShoppingCart,
      colorScheme: 'green' as const,
      onClick: () => onViewMore('purchaseOrders'),
      alert: false,
      subtitle: 'Awaiting processing'
    },
  ], [propertiesCount, criticalTasksCount, lowStockItemsCount, pendingOrdersCount, occupiedCount, vacantCount, onViewMore]);


  // Render Loading Skeleton for Cards
  const CardSkeleton = () => (
    <div className="animate-pulse h-48 bg-muted/50 rounded-md" />
  );

  // Render Empty State for Lists
  const EmptyListState = ({ icon: Icon, message }: { icon: React.ElementType, message: string }) => (
    <div className="text-center py-6 text-muted-foreground">
      <Icon className="w-6 h-6 mx-auto mb-2 opacity-40" />
      <p className="text-sm">{message}</p>
    </div>
  );

  return (
    <div className="space-y-1.5 w-full overflow-x-hidden">
      {/* Ultra-Compact Stat Cards - Mobile Optimized */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-1.5 w-full">
        {statCards.map((stat, index) => (
          <StatCard
            key={index}
            title={stat.title}
            value={stat.value}
            icon={stat.icon}
            colorScheme={stat.colorScheme}
            alert={stat.alert}
            loading={isLoading}
            subtitle={stat.subtitle}
            onClick={stat.onClick}
            className="animate-fade-in"
            style={{ animationDelay: `${index * 50}ms` }}
          />
        ))}
      </div>

      {/* Ultra-Compact Main Content Grid - Mobile Responsive */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-1.5 w-full">

        {/* Left Column (Overview Cards) - Mobile Optimized */}
        <div className="lg:col-span-2 space-y-1.5 w-full min-w-0">
          {/* Accordion Properties Overview */}
          <Card className="glass-card border-0 shadow-xl">
            <CardHeader className="py-1 px-2">
              <div className="flex items-center justify-between">
                <button
                  onClick={() => toggleSection('properties')}
                  className="flex items-center gap-1 text-xs hover:bg-muted/50 rounded p-1 -m-1 transition-colors text-foreground"
                >
                  <ChevronRight className={`h-3 w-3 text-blue-600 dark:text-blue-400 transition-transform duration-200 ${expandedSections.properties ? 'rotate-90' : ''}`} />
                  <Building2 className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                  <span className="font-medium text-foreground">Properties ({properties.length})</span>
                </button>
                <button
                  onClick={() => onViewMore('properties')}
                  className="text-xs text-primary dark:text-primary-foreground hover:underline flex items-center"
                >
                  View All <ArrowRight size={10} className="ml-0.5" />
                </button>
              </div>
            </CardHeader>
            {/* Always show summary, expand to show details */}
            <CardContent className="p-2">
              {/* Summary Row - Always Visible */}
              <div className="flex gap-2 mb-2">
                <div className="flex items-center gap-1.5 px-2 py-1 rounded bg-green-50 dark:bg-green-950/50 flex-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-xs font-medium text-green-700 dark:text-green-400">
                    {occupiedCount} Occupied
                  </span>
                </div>
                <div className="flex items-center gap-1.5 px-2 py-1 rounded bg-red-50 dark:bg-red-950/50 flex-1">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  <span className="text-xs font-medium text-red-700 dark:text-red-400">
                    {vacantCount} Vacant
                  </span>
                </div>
              </div>

              {/* Expanded Content */}
              {expandedSections.properties && (
                <div className="space-y-2 border-t pt-2 animate-in slide-in-from-top-2 duration-200">
                  {isLoading ? (
                    <div className="space-y-1">
                      <div className="h-4 bg-muted animate-pulse rounded"></div>
                      <div className="h-4 bg-muted animate-pulse rounded"></div>
                    </div>
                  ) : properties.length > 0 ? (
                    <>
                      {/* Property List */}
                      <div>
                        <div className="flex items-center gap-1 mb-1">
                          <Building2 className="h-3 w-3 text-blue-600" />
                          <span className="text-xs font-medium text-muted-foreground">All Properties</span>
                        </div>
                        <div className="space-y-1 max-h-32 overflow-y-auto overflow-x-hidden">
                          {properties.slice(0, 10).map(property => (
                            <div
                              key={property.id}
                              className="flex justify-between items-center text-xs hover:bg-muted/50 p-1 rounded cursor-pointer min-w-0"
                              onClick={() => handlePropertyClick(property.id)}
                            >
                              <span className="font-medium truncate flex-1 min-w-0">{property.name}</span>
                              <div className="flex items-center gap-1 flex-shrink-0">
                                <div className={`w-2 h-2 rounded-full ${property.is_occupied ? 'bg-green-500' : 'bg-red-500'}`}></div>
                                <span className={`text-xs ${property.is_occupied ? 'text-green-600' : 'text-red-600'}`}>
                                  {property.is_occupied ? 'Occupied' : 'Vacant'}
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Upcoming Check-ins */}
                      {upcomingCheckins.length > 0 && (
                        <div className="border-t pt-2">
                          <div className="flex items-center gap-1 mb-1">
                            <CalendarCheck className="h-3 w-3 text-teal-600" />
                            <span className="text-xs font-medium text-muted-foreground">Upcoming Check-ins</span>
                          </div>
                          <div className="space-y-1 max-h-24 overflow-y-auto overflow-x-hidden">
                            {upcomingCheckins.map(property => (
                              <div
                                key={property.id}
                                className="flex justify-between items-center text-xs hover:bg-muted/50 p-1 rounded cursor-pointer min-w-0"
                                onClick={() => handlePropertyClick(property.id)}
                              >
                                <span className="font-medium truncate flex-1 min-w-0">{property.name}</span>
                                <span className="text-teal-600 text-xs ml-2 flex-shrink-0">
                                  {property.daysUntil === 0 ? 'Today' :
                                   property.daysUntil === 1 ? 'Tomorrow' :
                                   `${property.daysUntil}d`}
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </>
                  ) : (
                    <div className="text-center py-2">
                      <Building2 className="w-4 h-4 mx-auto mb-1 opacity-40" />
                      <p className="text-xs text-muted-foreground">No properties</p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Accordion Maintenance Overview */}
          <Card className="glass-card border-0 shadow-xl">
            <CardHeader className="py-1 px-2">
              <div className="flex items-center justify-between">
                <button
                  onClick={() => toggleSection('maintenance')}
                  className="flex items-center gap-1 text-xs hover:bg-muted/50 rounded p-1 -m-1 transition-colors text-foreground"
                >
                  <ChevronRight className={`h-3 w-3 text-amber-600 dark:text-amber-400 transition-transform duration-200 ${expandedSections.maintenance ? 'rotate-90' : ''}`} />
                  <Wrench className="h-3 w-3 text-amber-600 dark:text-amber-400" />
                  <span className="font-medium text-foreground">Maintenance ({maintenanceTasks.length})</span>
                </button>
                <button
                  onClick={() => onViewMore('maintenance')}
                  className="text-xs text-primary dark:text-primary-foreground hover:underline flex items-center"
                >
                  View All <ArrowRight size={10} className="ml-0.5" />
                </button>
              </div>
            </CardHeader>
            <CardContent className="p-2">
              {/* Summary Row - Always Visible */}
              <div className="flex gap-1.5 mb-2">
                <div
                  className="flex items-center gap-1 px-2 py-1 rounded bg-red-50 dark:bg-red-950/50 flex-1 cursor-pointer hover:bg-red-100 dark:hover:bg-red-900/50"
                  onClick={() => {
                    window.sessionStorage.setItem('maintenance_severity_filter', 'critical');
                    navigate('/maintenance');
                  }}
                >
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  <span className="text-xs font-medium text-red-700 dark:text-red-400">
                    {maintenanceTasks.filter(task => task.severity === 'critical').length} Critical
                  </span>
                </div>
                <div
                  className="flex items-center gap-1 px-2 py-1 rounded bg-amber-50 dark:bg-amber-950/50 flex-1 cursor-pointer hover:bg-amber-100 dark:hover:bg-amber-900/50"
                  onClick={() => {
                    window.sessionStorage.setItem('maintenance_severity_filter', 'high');
                    navigate('/maintenance');
                  }}
                >
                  <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                  <span className="text-xs font-medium text-amber-700 dark:text-amber-400">
                    {maintenanceTasks.filter(task => task.severity === 'high').length} High
                  </span>
                </div>
              </div>

              {/* Expanded Content */}
              {expandedSections.maintenance && (
                <div className="space-y-2 border-t pt-2 animate-in slide-in-from-top-2 duration-200">
                  {isLoading ? (
                    <div className="space-y-1">
                      <div className="h-4 bg-muted animate-pulse rounded"></div>
                      <div className="h-4 bg-muted animate-pulse rounded"></div>
                    </div>
                  ) : maintenanceTasks && maintenanceTasks.length > 0 ? (
                    <>
                      {/* Critical Tasks */}
                      {maintenanceTasks.filter(task => task.severity === 'critical').length > 0 && (
                        <div>
                          <div className="flex items-center gap-1 mb-1">
                            <AlertCircle className="h-3 w-3 text-red-600" />
                            <span className="text-xs font-medium text-muted-foreground">Critical Tasks</span>
                          </div>
                          <div className="space-y-1 max-h-24 overflow-y-auto">
                            {maintenanceTasks.filter(task => task.severity === 'critical').slice(0, 5).map(task => (
                              <div
                                key={task.id}
                                className="flex justify-between items-center text-xs hover:bg-muted/50 p-1 rounded cursor-pointer"
                                onClick={() => navigate(`/maintenance?id=${task.id}`)}
                              >
                                <span className="font-medium truncate flex-1">{task.title}</span>
                                <span className="text-red-600 text-xs ml-2">{task.status}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* High Priority Tasks */}
                      {maintenanceTasks.filter(task => task.severity === 'high').length > 0 && (
                        <div className="border-t pt-2">
                          <div className="flex items-center gap-1 mb-1">
                            <AlertTriangle className="h-3 w-3 text-amber-600" />
                            <span className="text-xs font-medium text-muted-foreground">High Priority Tasks</span>
                          </div>
                          <div className="space-y-1 max-h-24 overflow-y-auto">
                            {maintenanceTasks.filter(task => task.severity === 'high').slice(0, 5).map(task => (
                              <div
                                key={task.id}
                                className="flex justify-between items-center text-xs hover:bg-muted/50 p-1 rounded cursor-pointer"
                                onClick={() => navigate(`/maintenance?id=${task.id}`)}
                              >
                                <span className="font-medium truncate flex-1">{task.title}</span>
                                <span className="text-amber-600 text-xs ml-2">{task.status}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Status Summary */}
                      <div className="border-t pt-2">
                        <div className="flex gap-1 text-xs">
                          <div
                            className="flex items-center gap-1 px-1.5 py-0.5 rounded bg-blue-50 dark:bg-blue-950/50 cursor-pointer hover:bg-blue-100 dark:hover:bg-blue-900/50"
                            onClick={() => {
                              window.sessionStorage.setItem('maintenance_status_filter', 'new');
                              navigate('/maintenance');
                            }}
                          >
                            <span className="text-blue-700 dark:text-blue-400">New:</span>
                            <span className="font-semibold text-blue-700 dark:text-blue-400">{maintenanceTasks.filter(task => task.status === 'new').length}</span>
                          </div>
                          <div
                            className="flex items-center gap-1 px-1.5 py-0.5 rounded bg-purple-50 dark:bg-purple-950/50 cursor-pointer hover:bg-purple-100 dark:hover:bg-purple-900/50"
                            onClick={() => {
                              window.sessionStorage.setItem('maintenance_status_filter', 'in_progress');
                              navigate('/maintenance');
                            }}
                          >
                            <span className="text-purple-700 dark:text-purple-400">Active:</span>
                            <span className="font-semibold text-purple-700 dark:text-purple-400">{maintenanceTasks.filter(task => task.status === 'in_progress' || task.status === 'assigned').length}</span>
                          </div>
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="text-center py-2">
                      <Wrench className="w-4 h-4 mx-auto mb-1 opacity-40" />
                      <p className="text-xs text-muted-foreground">No maintenance tasks</p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Right Column (Accordion Action Items) - Mobile Optimized */}
        <div className="lg:col-span-1 w-full min-w-0">
          <Card className="glass-card border-0 shadow-xl">
            <CardHeader className="py-1 px-2">
              <CardTitle className="text-xs flex items-center gap-1 text-foreground">
                <Activity className="h-3 w-3 text-foreground" />
                Action Items
              </CardTitle>
            </CardHeader>
            <CardContent className="p-2">
              <div className="space-y-1.5">
                {/* Critical Tasks Accordion */}
                {criticalTasks.length > 0 && (
                  <div className="border rounded bg-red-50 dark:bg-red-950/50">
                    <button
                      onClick={() => toggleSection('criticalTasks')}
                      className="w-full flex items-center justify-between p-1.5 hover:bg-red-100 dark:hover:bg-red-900/50 transition-colors text-foreground"
                    >
                      <div className="flex items-center gap-1.5">
                        <ChevronRight className={`h-3 w-3 text-red-600 dark:text-red-400 transition-transform duration-200 ${expandedSections.criticalTasks ? 'rotate-90' : ''}`} />
                        <Wrench className="h-3 w-3 text-red-600 dark:text-red-400" />
                        <span className="text-xs font-medium text-red-700 dark:text-red-300">Critical Tasks</span>
                      </div>
                      <span className="text-xs font-bold text-red-700 dark:text-red-300">{criticalTasks.length}</span>
                    </button>
                    {expandedSections.criticalTasks && (
                      <div className="border-t border-red-200 dark:border-red-800 p-1.5 space-y-1 max-h-32 overflow-y-auto overflow-x-hidden animate-in slide-in-from-top-2 duration-200">
                        {criticalTasks.slice(0, 10).map(task => (
                          <div
                            key={task.id}
                            className="flex justify-between items-center text-xs hover:bg-red-100 dark:hover:bg-red-900/50 p-1 rounded cursor-pointer min-w-0"
                            onClick={() => navigate(`/maintenance?id=${task.id}`)}
                          >
                            <span className="font-medium truncate flex-1 min-w-0">{task.title}</span>
                            <span className="text-red-600 text-xs ml-2 flex-shrink-0">{task.status}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                {/* Low Stock Accordion */}
                {lowStockItems.length > 0 && (
                  <div className="border rounded bg-purple-50 dark:bg-purple-950/50">
                    <button
                      onClick={() => toggleSection('lowStock')}
                      className="w-full flex items-center justify-between p-1.5 hover:bg-purple-100 dark:hover:bg-purple-900/50 transition-colors text-foreground"
                    >
                      <div className="flex items-center gap-1.5">
                        <ChevronRight className={`h-3 w-3 text-purple-600 dark:text-purple-400 transition-transform duration-200 ${expandedSections.lowStock ? 'rotate-90' : ''}`} />
                        <Package className="h-3 w-3 text-purple-600 dark:text-purple-400" />
                        <span className="text-xs font-medium text-purple-700 dark:text-purple-300">Low Stock</span>
                      </div>
                      <span className="text-xs font-bold text-purple-700 dark:text-purple-300">{lowStockItems.length}</span>
                    </button>
                    {expandedSections.lowStock && (
                      <div className="border-t border-purple-200 dark:border-purple-800 p-1.5 space-y-1 max-h-32 overflow-y-auto overflow-x-hidden animate-in slide-in-from-top-2 duration-200">
                        {lowStockItems.slice(0, 10).map(item => (
                          <div
                            key={item.id}
                            className="flex justify-between items-center text-xs hover:bg-purple-100 dark:hover:bg-purple-900/50 p-1 rounded cursor-pointer min-w-0"
                            onClick={() => navigate(`/inventory?itemId=${item.id}`)}
                          >
                            <span className="font-medium truncate flex-1 min-w-0">{item.name}</span>
                            <span className="text-purple-600 text-xs ml-2 flex-shrink-0">{item.quantity}/{item.minQuantity}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                {/* New Damages Accordion */}
                {newDamages.length > 0 && (
                  <div className="border rounded bg-blue-50 dark:bg-blue-950/50">
                    <button
                      onClick={() => toggleSection('newDamages')}
                      className="w-full flex items-center justify-between p-1.5 hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors text-foreground"
                    >
                      <div className="flex items-center gap-1.5">
                        <ChevronRight className={`h-3 w-3 text-blue-600 dark:text-blue-400 transition-transform duration-200 ${expandedSections.newDamages ? 'rotate-90' : ''}`} />
                        <AlertCircle className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                        <span className="text-xs font-medium text-blue-700 dark:text-blue-300">New Damages</span>
                      </div>
                      <span className="text-xs font-bold text-blue-700 dark:text-blue-300">{newDamages.length}</span>
                    </button>
                    {expandedSections.newDamages && (
                      <div className="border-t border-blue-200 dark:border-blue-800 p-1.5 space-y-1 max-h-32 overflow-y-auto overflow-x-hidden animate-in slide-in-from-top-2 duration-200">
                        {newDamages.slice(0, 10).map(damage => (
                          <div
                            key={damage.id}
                            className="flex justify-between items-center text-xs hover:bg-blue-100 dark:hover:bg-blue-900/50 p-1 rounded cursor-pointer min-w-0"
                            onClick={() => navigate(`/damages/${damage.id}`)}
                          >
                            <span className="font-medium truncate flex-1 min-w-0">{damage.title}</span>
                            <span className="text-blue-600 text-xs ml-2 flex-shrink-0">{damage.status}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                {/* Pending Orders Accordion */}
                {pendingOrders.length > 0 && (
                  <div className="border rounded bg-green-50 dark:bg-green-950/50">
                    <button
                      onClick={() => toggleSection('pendingOrders')}
                      className="w-full flex items-center justify-between p-1.5 hover:bg-green-100 dark:hover:bg-green-900/50 transition-colors text-foreground"
                    >
                      <div className="flex items-center gap-1.5">
                        <ChevronRight className={`h-3 w-3 text-green-600 dark:text-green-400 transition-transform duration-200 ${expandedSections.pendingOrders ? 'rotate-90' : ''}`} />
                        <ShoppingCart className="h-3 w-3 text-green-600 dark:text-green-400" />
                        <span className="text-xs font-medium text-green-700 dark:text-green-300">Pending Orders</span>
                      </div>
                      <span className="text-xs font-bold text-green-700 dark:text-green-300">{pendingOrders.length}</span>
                    </button>
                    {expandedSections.pendingOrders && (
                      <div className="border-t border-green-200 dark:border-green-800 p-1.5 space-y-1 max-h-32 overflow-y-auto overflow-x-hidden animate-in slide-in-from-top-2 duration-200">
                        {pendingOrders.slice(0, 10).map(order => (
                          <div
                            key={order.id}
                            className="flex justify-between items-center text-xs hover:bg-green-100 dark:hover:bg-green-900/50 p-1 rounded cursor-pointer min-w-0"
                            onClick={() => handleOrderClick(order)}
                          >
                            <span className="font-medium truncate flex-1 min-w-0">Order #{order.id.substring(0, 6)}</span>
                            <span className="text-green-600 text-xs ml-2 flex-shrink-0">{order.status}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                {/* Upcoming Check-ins Accordion */}
                {upcomingCheckins.length > 0 && (
                  <div className="border rounded bg-teal-50 dark:bg-teal-950/50">
                    <button
                      onClick={() => toggleSection('checkins')}
                      className="w-full flex items-center justify-between p-1.5 hover:bg-teal-100 dark:hover:bg-teal-900/50 transition-colors text-foreground"
                    >
                      <div className="flex items-center gap-1.5">
                        <ChevronRight className={`h-3 w-3 text-teal-600 dark:text-teal-400 transition-transform duration-200 ${expandedSections.checkins ? 'rotate-90' : ''}`} />
                        <CalendarCheck className="h-3 w-3 text-teal-600 dark:text-teal-400" />
                        <span className="text-xs font-medium text-teal-700 dark:text-teal-300">Check-ins</span>
                      </div>
                      <span className="text-xs font-bold text-teal-700 dark:text-teal-300">{upcomingCheckins.length}</span>
                    </button>
                    {expandedSections.checkins && (
                      <div className="border-t border-teal-200 dark:border-teal-800 p-1.5 space-y-1 max-h-32 overflow-y-auto overflow-x-hidden animate-in slide-in-from-top-2 duration-200">
                        {upcomingCheckins.slice(0, 10).map(property => (
                          <div
                            key={property.id}
                            className="flex justify-between items-center text-xs hover:bg-teal-100 dark:hover:bg-teal-900/50 p-1 rounded cursor-pointer min-w-0"
                            onClick={() => handlePropertyClick(property.id)}
                          >
                            <span className="font-medium truncate flex-1 min-w-0">{property.name}</span>
                            <span className="text-teal-600 text-xs ml-2 flex-shrink-0">
                              {property.daysUntil === 0 ? 'Today' :
                               property.daysUntil === 1 ? 'Tomorrow' :
                               `${property.daysUntil}d`}
                            </span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                {/* Empty State */}
                {criticalTasks.length === 0 && lowStockItems.length === 0 && newDamages.length === 0 && pendingOrders.length === 0 && upcomingCheckins.length === 0 && (
                  <div className="text-center py-4">
                    <Activity className="w-4 h-4 mx-auto mb-1 opacity-40" />
                    <p className="text-xs text-muted-foreground">All caught up!</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

// --- Compact Item Components (Potentially move to separate files later) ---

// Compact Task Item (Added Severity)
const CompactTaskItem = ({ title, subtitle, status, dueDate, onClick, severity }: {
  title: string;
  subtitle: string;
  status: string;
  dueDate: string;
  onClick: () => void;
  severity?: 'low' | 'medium' | 'high' | 'critical';
}) => {
  const statusStyles = useMemo(() => {
    switch (status) {
      case 'new': return { icon: Circle, color: 'text-blue-500 dark:text-blue-400', bg: 'bg-blue-100 dark:bg-blue-950/50' };
      case 'assigned': return { icon: Circle, color: 'text-purple-500 dark:text-purple-400', bg: 'bg-purple-100 dark:bg-purple-950/50' };
      case 'in_progress': return { icon: Circle, color: 'text-yellow-500 dark:text-yellow-400', bg: 'bg-yellow-100 dark:bg-yellow-950/50' };
      case 'completed': return { icon: Circle, color: 'text-green-500 dark:text-green-400', bg: 'bg-green-100 dark:bg-green-950/50' };
      default: return { icon: Circle, color: 'text-gray-500 dark:text-gray-400', bg: 'bg-gray-100 dark:bg-gray-800' };
    }
  }, [status]);

  const severityStyles = useMemo(() => {
    switch (severity) {
      case 'critical': return "border-l-4 border-red-500";
      case 'high': return "border-l-4 border-amber-500";
      default: return "border-l-4 border-transparent";
    }
  }, [severity]);

  return (
    <div
      className={cn("flex items-center p-2 rounded-md hover:bg-muted/50 cursor-pointer transition-colors", severityStyles)}
      onClick={onClick}
    >
      <span className={cn("mr-2 p-0.5 rounded-full", statusStyles.bg)}>
        <statusStyles.icon className={cn("h-2 w-2", statusStyles.color)} fill={statusStyles.color} />
      </span>
      <div className="flex-1 min-w-0">
        <p className="text-xs font-medium truncate">{title}</p>
        <p className="text-xs text-muted-foreground truncate">{subtitle}</p>
      </div>
      <div className="ml-2 text-right">
        <p className="text-xs text-muted-foreground whitespace-nowrap">{dueDate}</p>
        <p className="text-xs capitalize font-medium" style={{ color: statusStyles.color }}>{status.replace('_', ' ')}</p>
      </div>
    </div>
  );
};

// Compact Inventory Item
const CompactInventoryItem = ({ name, quantity, minQuantity, onClick }: {
  name: string;
  quantity: number | null | undefined;
  minQuantity: number | null | undefined;
  onClick: () => void;
}) => {
  const isLow = typeof quantity === 'number' && typeof minQuantity === 'number' && quantity <= minQuantity;
  return (
    <div
      className={cn(
        "flex items-center p-2 rounded-md hover:bg-muted/50 cursor-pointer transition-colors",
        isLow && "border-l-4 border-purple-500"
      )}
      onClick={onClick}
    >
      <span className={cn("mr-2 p-0.5 rounded-full", isLow ? "bg-purple-100 dark:bg-purple-950/50" : "bg-gray-100 dark:bg-gray-800")}>
        <Package className={cn("h-3 w-3", isLow ? "text-purple-600 dark:text-purple-400" : "text-gray-500 dark:text-gray-400")} />
      </span>
      <div className="flex-1 min-w-0">
        <p className="text-xs font-medium truncate">{name}</p>
      </div>
      <div className="ml-2 text-right">
        <p className={cn("text-xs font-semibold", isLow ? "text-purple-600 dark:text-purple-400" : "text-gray-700 dark:text-gray-300")}>
          {quantity ?? 'N/A'}
        </p>
        <p className="text-xs text-muted-foreground">Min: {minQuantity ?? 'N/A'}</p>
      </div>
    </div>
  );
};

// Compact Damage Item
const CompactDamageItem = ({ title, propertyName, reportedAt, status, onClick }: {
  title: string;
  propertyName: string;
  reportedAt: string;
  status: 'new' | 'pending' | 'completed';
  onClick: () => void;
}) => {
  const statusStyles = useMemo(() => {
    switch (status) {
      case 'new': return { icon: AlertCircle, color: 'text-blue-500 dark:text-blue-400', bg: 'bg-blue-100 dark:bg-blue-950/50' };
      case 'pending': return { icon: AlertCircle, color: 'text-amber-500 dark:text-amber-400', bg: 'bg-amber-100 dark:bg-amber-950/50' };
      case 'completed': return { icon: AlertCircle, color: 'text-green-500 dark:text-green-400', bg: 'bg-green-100 dark:bg-green-950/50' };
      default: return { icon: AlertCircle, color: 'text-gray-500 dark:text-gray-400', bg: 'bg-gray-100 dark:bg-gray-800' };
    }
  }, [status]);

  return (
    <div
      className={cn(
        "flex items-center p-2 rounded-md hover:bg-muted/50 cursor-pointer transition-colors",
        status === 'new' && "border-l-4 border-blue-500" // Highlight new damages
      )}
      onClick={onClick}
    >
      <span className={cn("mr-2 p-0.5 rounded-full", statusStyles.bg)}>
        <statusStyles.icon className={cn("h-3 w-3", statusStyles.color)} />
      </span>
      <div className="flex-1 min-w-0">
        <p className="text-xs font-medium truncate">{title}</p>
        <p className="text-xs text-muted-foreground truncate">{propertyName}</p>
      </div>
      <div className="ml-2 text-right">
        <p className="text-xs text-muted-foreground whitespace-nowrap">{reportedAt}</p>
         <p className="text-xs capitalize font-medium" style={{ color: statusStyles.color }}>{status}</p>
      </div>
    </div>
  );
};

// Compact Order Item
const CompactOrderItem = ({ orderNumber, supplier, status, itemCount, onClick }: {
  orderNumber: string;
  supplier: string; // Changed from 'supplier' to represent property_name now
  status: string;
  itemCount: number;
  onClick: () => void;
}) => {
   const statusStyles = useMemo(() => {
    switch (status) {
      case 'pending': return { icon: ShoppingCart, color: 'text-green-500 dark:text-green-400', bg: 'bg-green-100 dark:bg-green-950/50' };
      case 'ordered': return { icon: ShoppingCart, color: 'text-blue-500 dark:text-blue-400', bg: 'bg-blue-100 dark:bg-blue-950/50' };
      case 'received': return { icon: ShoppingCart, color: 'text-purple-500 dark:text-purple-400', bg: 'bg-purple-100 dark:bg-purple-950/50' };
      default: return { icon: ShoppingCart, color: 'text-gray-500 dark:text-gray-400', bg: 'bg-gray-100 dark:bg-gray-800' };
    }
  }, [status]);

  return (
    <div
      className={cn(
        "flex items-center p-2 rounded-md hover:bg-muted/50 cursor-pointer transition-colors",
         status === 'pending' && "border-l-4 border-green-500" // Highlight pending orders
        )}
      onClick={onClick}
    >
       <span className={cn("mr-2 p-0.5 rounded-full", statusStyles.bg)}>
        <statusStyles.icon className={cn("h-3 w-3", statusStyles.color)} />
      </span>
      <div className="flex-1 min-w-0">
        <p className="text-xs font-medium truncate">{orderNumber}</p>
        {/* Display property name as subtitle */}
        <p className="text-xs text-muted-foreground truncate">{supplier}</p>
      </div>
      <div className="ml-2 text-right">
        <p className="text-xs text-muted-foreground">{itemCount} item(s)</p>
         <p className="text-xs capitalize font-medium" style={{ color: statusStyles.color }}>{status}</p>
      </div>
    </div>
  );
};

// Compact Check-in Item
const CompactCheckinItem = ({ propertyName, daysUntil, onClick }: {
  propertyName: string;
  daysUntil: number;
  onClick: () => void;
}) => {
  const urgencyColor = useMemo(() => {
    if (daysUntil === 0) return "text-blue-600"; // Today
    if (daysUntil <= 3) return "text-amber-600"; // Soon
    return "text-green-600"; // Further out
  }, [daysUntil]);

  const urgencyText = useMemo(() => {
    if (daysUntil === 0) return "Today";
    if (daysUntil === 1) return "Tomorrow";
    return `In ${daysUntil} days`;
  }, [daysUntil]);

  return (
    <div
      className={cn(
        "flex items-center p-2 rounded-md hover:bg-muted/50 cursor-pointer transition-colors",
        daysUntil <= 1 && "border-l-4 border-blue-500" // Highlight today/tomorrow
      )}
      onClick={onClick}
    >
      <span className={cn("mr-2 p-0.5 rounded-full", daysUntil <= 1 ? "bg-blue-100 dark:bg-blue-950/50" : "bg-teal-100 dark:bg-teal-950/50")}>
        <CalendarCheck className={cn("h-3 w-3", daysUntil <= 1 ? "text-blue-600 dark:text-blue-400" : "text-teal-600 dark:text-teal-400")} />
      </span>
      <div className="flex-1 min-w-0">
        <p className="text-xs font-medium truncate">{propertyName}</p>
      </div>
      <div className="ml-2 text-right">
        <p className={cn("text-xs font-semibold whitespace-nowrap", urgencyColor)}>
          {urgencyText}
        </p>
      </div>
    </div>
  );
};


export default memo(DashboardView);
