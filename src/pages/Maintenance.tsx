import { useState, useEffect, useCallback, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { useSearchParams, Link } from 'react-router-dom';
import { Plus, Filter, AlertCircle, Sparkles, Refresh<PERSON>w, Wrench, Calendar, Trash2, Loader2 } from 'lucide-react';
import { StandardPageHeader, PageHeaderButtons } from '@/components/ui/StandardizedUI';
import MaintenanceTabs from '@/components/maintenance/MaintenanceTabs';
import { MaintenanceTask } from '@/components/maintenance/types';
import { useMaintenanceTasksQueryV2 } from '@/hooks/useMaintenanceTasksQueryV2'; // Using the new hook
import AddMaintenanceDialog from '@/components/maintenance/AddMaintenanceDialog';
import MaintenanceDetailsDialog from '@/components/maintenance/MaintenanceDetailsDialog';
import AiMaintenanceDialog from '@/components/maintenance/AiMaintenanceDialog';
import PageTransition from '@/components/layout/PageTransition';
import { Provider } from '@/components/maintenance/types';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { useQueryClient } from '@tanstack/react-query';

const Maintenance = () => {
  const [searchParams] = useSearchParams();
  const initialTaskId = searchParams.get('id');
  const queryClient = useQueryClient();
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Use our new standardized React Query hook
  const {
    tasks,
    loading,
    error: tasksError,
    fetchTasks,
    updateTaskStatus,
    deleteTask
  } = useMaintenanceTasksQueryV2();

  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isAiDialogOpen, setIsAiDialogOpen] = useState(false);
  const [selectedTask, setSelectedTask] = useState<MaintenanceTask | null>(null);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [providers, setProviders] = useState<Provider[]>([]);
  const [providersLoading, setProvidersLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isFiltersOpen, setIsFiltersOpen] = useState(false);
  const { authState } = useAuth();
  const userId = authState.user?.id;

  // Debug auth state on maintenance page
  console.log('[Maintenance] Auth state:', {
    isLoading: authState?.isLoading,
    isAuthenticated: authState?.isAuthenticated,
    userId,
    userEmail: authState?.user?.email
  });

  // Don't render until auth is ready
  if (authState?.isLoading) {
    return (
      <PageTransition>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">Loading...</p>
          </div>
        </div>
      </PageTransition>
    );
  }

  useEffect(() => {
    const status = searchParams.get('status');
    const message = searchParams.get('message');
    const taskId = searchParams.get('taskId');

    if (status && message) {
      if (status === 'success') {
        toast.success(message);
      } else if (status === 'error') {
        toast.error(message);
      } else if (status === 'info') {
        toast.info(message);
      }

      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);

      if (taskId) {
        console.log("Task status updated, refreshing data...");
        fetchTasks();
      }
    }
  }, [searchParams, fetchTasks]);

  // Extract fetchProviders as a reusable function
  const fetchProviders = useCallback(async () => {
    if (!userId) return;

    try {
      setProvidersLoading(true);
      setError(null);

      // First, fetch the user's teams
      const { data: teamData, error: teamsError } = await supabase
        .from('team_members')
        .select('team_id')
        .eq('user_id', userId)
        .eq('status', 'active');

      if (teamsError) throw teamsError;

      const teamIds = teamData?.map((t: { team_id: string }) => t.team_id) || [];
      console.log('[Maintenance] User teams:', teamIds);

      // Check if user is admin or super admin
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('is_super_admin, role')
        .eq('id', userId)
        .single();

      if (profileError) throw profileError;

      const isAdmin = profileData?.is_super_admin || profileData?.role === 'admin';
      console.log('[Maintenance] User is admin:', isAdmin);

      // Fetch the user's own providers
      const { data: ownProviders, error: ownError } = await supabase
        .from('maintenance_providers')
        .select('*')
        .eq('user_id', userId);

      if (ownError) throw ownError;
      console.log('[Maintenance] Own providers:', ownProviders?.length);

      // Fetch all providers using our RPC function
      const { data: allProviders, error: providersError } = await supabase.rpc(
        'get_providers'
      );

      if (providersError) {
        console.error('[Maintenance] Error fetching providers with RPC:', providersError);
        // Continue with just own providers if there's an error

        // Use only own providers
        const data = [...(ownProviders || [])];
        console.log('[Maintenance] Using only own providers:', data.length);
      } else {
        console.log('[Maintenance] All providers from RPC:', allProviders?.length);

        // Use all providers from RPC
        const data = [...(allProviders || [])];
        console.log('[Maintenance] Using providers from RPC:', data.length);

        // Format and set providers
        if (data && data.length > 0) {
          console.log('[Maintenance] Fetched providers:', data);
          const formattedProviders: Provider[] = data.map(provider => {
            return {
              id: provider.id,
              name: provider.name || '',
              email: provider.email || '',
              phone: provider.phone || '',
              specialty: provider.specialty || '',
              address: provider.notes || '',
              user_id: provider.user_id
            };
          });

          console.log('[Maintenance] Formatted providers:', formattedProviders);
          setProviders(formattedProviders);
          setProvidersLoading(false);
          return;
        }
      }

      // If we get here, we need to use the own providers as fallback
      if (ownProviders && ownProviders.length > 0) {
        console.log('[Maintenance] Using fallback with own providers:', ownProviders.length);
        const formattedProviders: Provider[] = ownProviders.map((provider: any) => {
          return {
            id: provider.id,
            name: provider.name || '',
            email: provider.email || '',
            phone: provider.phone || '',
            specialty: provider.specialty || '',
            address: provider.notes || '',
            user_id: provider.user_id
          };
        });

        console.log('[Maintenance] Formatted own providers:', formattedProviders);
        setProviders(formattedProviders);
      } else {
        console.log('[Maintenance] No providers available');
        setProviders([]);
      }
    } catch (error: any) {
      console.error('Error fetching providers:', error);
      setError(`Failed to load service providers: ${error.message}`);
      toast.error('Failed to load service providers');
    } finally {
      setProvidersLoading(false);
    }
  }, [userId]);

  // Fetch providers when component mounts
  useEffect(() => {
    fetchProviders();
  }, [fetchProviders]);

  // Add an explicit effect to fetch tasks when the component mounts
  // Use a ref to prevent duplicate fetches
  const initialLoadDoneRef = useRef(false);

  useEffect(() => {
    // Skip if we've already done the initial load
    if (initialLoadDoneRef.current) return;

    // Mark that we've done the initial load
    initialLoadDoneRef.current = true;

    console.log('[Maintenance] Component mounted, fetching tasks...');
    fetchTasks();
  }, [fetchTasks]);

  // Event handlers for network, force refresh events (removed visibility change)
  useEffect(() => {
    // Function to handle global data refresh events
    const handleDataRefreshed = (event: CustomEvent) => {
      if (loading) return; // Already loading, skip

        // Get the current route path
        const currentPath = window.location.pathname;
        // Check if this event is for us (either no route specified or includes '/maintenance')
        const refreshedRoute = event.detail?.route || '';
        if (!refreshedRoute || refreshedRoute === '/maintenance' || currentPath.includes('/maintenance')) {
          console.log('[Maintenance] Received data refresh event, refreshing data');
          fetchTasks(); // Use the hook's refresh function directly
        }
    };

    // Function to handle network status changes
    const handleNetworkOnline = () => {
      if (loading) return; // Already loading, skip
        console.log('[Maintenance] Network is back online, refreshing data');
        fetchTasks(); // Use the hook's refresh function directly
    };

    // Function to handle force refresh
    const handleForceRefresh = () => {
      if (loading) return; // Already loading, skip
        console.log('[Maintenance] Force refresh triggered, refreshing data');
        fetchTasks(); // Use the hook's refresh function directly
    };

    // Set up event listeners (removed visibility change to prevent data disappearing)
    window.addEventListener('stayfu-data-refreshed', handleDataRefreshed as EventListener);
    window.addEventListener('stayfu-network-online', handleNetworkOnline);
    window.addEventListener('force-refresh-maintenance', handleForceRefresh);

    // Clean up event listeners
    return () => {
      window.removeEventListener('stayfu-data-refreshed', handleDataRefreshed as EventListener);
      window.removeEventListener('stayfu-network-online', handleNetworkOnline);
      window.removeEventListener('force-refresh-maintenance', handleForceRefresh);
    };
  }, [fetchTasks, loading, queryClient]); // Removed isRefreshing from dependencies as it's for the manual button

  useEffect(() => {
    // Handle task ID from URL
    if (initialTaskId && tasks.length > 0) {
      const task = tasks.find((t: MaintenanceTask) => t.id === initialTaskId);
      if (task) {
        setSelectedTask(task);
        setIsDetailsDialogOpen(true);
      }
    }

    // Handle status filter from URL
    const statusParam = searchParams.get('status');
    if (statusParam) {
      console.log('Setting status filter from URL:', statusParam);
      // This will be passed to MaintenanceTabs component
      window.sessionStorage.setItem('maintenance_status_filter', statusParam);
    }

    // Handle severity filter from URL
    const severityParam = searchParams.get('severity');
    if (severityParam) {
      console.log('Setting severity filter from URL:', severityParam);
      // Handle the special case for 'normal' which maps to medium or low
      if (severityParam === 'normal') {
        window.sessionStorage.setItem('maintenance_severity_filter', 'medium');
      } else {
        window.sessionStorage.setItem('maintenance_severity_filter', severityParam);
      }
    }

    // Handle provider filter from URL
    const providerParam = searchParams.get('provider');
    if (providerParam) {
      console.log('Setting provider filter from URL:', providerParam);
      window.sessionStorage.setItem('maintenance_provider_filter', providerParam);
    }

    // Handle property filter from URL
    const propertyParam = searchParams.get('property');
    if (propertyParam) {
      console.log('Setting property filter from URL:', propertyParam);
      window.sessionStorage.setItem('maintenance_property_filter', propertyParam);
    }
  }, [initialTaskId, tasks, searchParams]);

  const handleOpenAddDialog = () => {
    setIsAddDialogOpen(true);
  };

  const handleOpenAiDialog = () => {
    setIsAiDialogOpen(true);
  };

  const handleTaskCreated = () => {
    fetchTasks();
    setIsAddDialogOpen(false);
  };

  const handleAiTasksCreated = () => {
    fetchTasks();
    setIsAiDialogOpen(false);
  };

  const handleTaskClick = (task: MaintenanceTask) => {
    setSelectedTask(task);
    setIsDetailsDialogOpen(true);
  };

  const handleStatusChange = async (taskId: string, newStatus: MaintenanceTask['status']): Promise<boolean> => {
    try {
      console.log(`[Maintenance] Changing task status: ${taskId} to ${newStatus}`);
      console.log(`[Maintenance] User ID: ${userId}`);
      console.log(`[Maintenance] Auth state:`, authState?.user);

      const result = await updateTaskStatus(taskId, newStatus);
      console.log(`[Maintenance] Status change result:`, result);

      if (result) {
        toast.success(`Task status updated to ${newStatus}`);
        await fetchTasks();
      } else {
        console.error(`[Maintenance] Status change failed for task ${taskId}`);
        toast.error('Failed to update task status');
      }
      return result;
    } catch (error) {
      console.error('[Maintenance] Exception changing task status:', error);
      toast.error(`Error changing task status: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return false;
    }
  };

  const handleDeleteTask = async (taskId: string): Promise<boolean> => {
    try {
      console.log(`[Maintenance] Deleting task: ${taskId}`);
      console.log(`[Maintenance] User ID: ${userId}`);
      console.log(`[Maintenance] Auth state:`, authState?.user);

      const result = await deleteTask(taskId);
      console.log(`[Maintenance] Delete result:`, result);

      if (result) {
        setIsDetailsDialogOpen(false);
        toast.success('Task deleted successfully');
      } else {
        console.error(`[Maintenance] Delete failed for task ${taskId}`);
        toast.error('Failed to delete task');
      }
      return result;
    } catch (error) {
      console.error('[Maintenance] Exception deleting task:', error);
      toast.error(`Error deleting task: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return false;
    }
  };

  const handleTaskUpdated = () => {
    fetchTasks();
  };

  const handleAddProvider = async (provider: Omit<Provider, 'id'>) => {
    try {
      const { data, error } = await supabase
        .from('maintenance_providers')
        .insert({
          name: provider.name,
          email: provider.email,
          phone: provider.phone,
          specialty: provider.specialty,
          notes: provider.address,
          user_id: userId
        })
        .select()
        .single();

      if (error) throw error;

      if (data) {
        const newProvider: Provider = {
          id: data.id,
          name: data.name,
          email: data.email || '',
          phone: data.phone || '',
          specialty: data.specialty || '',
          address: data.notes || ''
        };

        setProviders(prev => [...prev, newProvider]);
        toast.success('Provider added successfully');
      }
    } catch (error: any) {
      console.error('Error adding provider:', error);
      toast.error(`Failed to add provider: ${error.message}`);
    }
  };

  const handleUpdateProvider = async (id: string, provider: Omit<Provider, 'id'>) => {
    try {
      const { error } = await supabase
        .from('maintenance_providers')
        .update({
          name: provider.name,
          email: provider.email,
          phone: provider.phone,
          specialty: provider.specialty,
          notes: provider.address
        })
        .eq('id', id)
        .eq('user_id', userId);

      if (error) throw error;

      setProviders(prev =>
        prev.map(p => p.id === id ? { ...p, ...provider } : p)
      );

      toast.success('Provider updated successfully');
    } catch (error: any) {
      console.error('Error updating provider:', error);
      toast.error(`Failed to update provider: ${error.message}`);
    }
  };

  const handleDeleteProvider = async (id: string) => {
    try {
      const { error } = await supabase
        .from('maintenance_providers')
        .delete()
        .eq('id', id)
        .eq('user_id', userId);

      if (error) throw error;

      setProviders(prev => prev.filter(p => p.id !== id));
      toast.success('Provider deleted successfully');
    } catch (error: any) {
      console.error('Error deleting provider:', error);
      toast.error(`Failed to delete provider: ${error.message}`);
    }
  };

  // Keep track of whether we have data to display
  const hasData = tasks.length > 0;

  return (
    <PageTransition>
      <div className="space-y-3 max-w-full overflow-x-hidden">

        {(error || tasksError) && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error || tasksError}
            </AlertDescription>
          </Alert>
        )}

        <MaintenanceTabs
          tasks={tasks}
          providers={providers}
          loading={loading || providersLoading}
          onTaskClick={handleTaskClick}
          onStatusChange={handleStatusChange}
          onDeleteTask={handleDeleteTask}
          onAddProvider={handleAddProvider}
          onUpdateProvider={handleUpdateProvider}
          onDeleteProvider={handleDeleteProvider}
          actionButtons={
            <div className="flex flex-wrap gap-1.5 sm:gap-2 items-center">
              <Button
                onClick={handleOpenAddDialog}
                size="sm"
                className="flex-shrink-0"
              >
                <Plus className="h-4 w-4 mr-1" />
                <span className="hidden sm:inline">Add Task</span>
                <span className="sm:hidden">Add</span>
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={async () => {
                  if (isRefreshing || loading) return;

                  try {
                    setIsRefreshing(true);
                    console.log('[Maintenance] Manual refresh triggered');

                    // Invalidate all maintenance-related queries
                    queryClient.invalidateQueries({ queryKey: ['maintenanceTasksV2'] });

                    // Call the hook's refresh function
                    await fetchTasks();

                    // Also refresh providers
                    await fetchProviders();

                    console.log('[Maintenance] Data refreshed successfully');
                  } catch (err) {
                    console.error('[Maintenance] Error refreshing data:', err);
                  } finally {
                    setIsRefreshing(false);
                  }
                }}
                disabled={loading || isRefreshing}
                className="flex-shrink-0"
              >
                <RefreshCw className={`h-4 w-4 ${loading || isRefreshing ? 'animate-spin' : ''}`} />
                <span className="sr-only">Refresh</span>
              </Button>

              {authState?.profile?.role !== 'service_provider' && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    asChild
                    className="bg-blue-50 hover:bg-blue-100 border-blue-200 dark:bg-blue-950/20 dark:hover:bg-blue-900/30 dark:border-blue-800/30 flex-shrink-0"
                  >
                    <Link to="/maintenance/automation">
                      <Calendar className="h-4 w-4 mr-1 text-blue-500 dark:text-blue-400" />
                      <span className="hidden sm:inline">Task Automation</span>
                      <span className="sm:hidden">Auto</span>
                    </Link>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleOpenAiDialog}
                    className="bg-purple-50 hover:bg-purple-100 border-purple-200 dark:bg-purple-950/20 dark:hover:bg-purple-900/30 dark:border-purple-800/30 flex-shrink-0"
                  >
                    <Sparkles className="h-4 w-4 mr-1 text-purple-500 dark:text-purple-400" />
                    <span className="hidden sm:inline">AI Generate</span>
                    <span className="sm:hidden">AI</span>
                  </Button>
                </>
              )}
            </div>
          }
        />

        {isAddDialogOpen && (
          <AddMaintenanceDialog
            open={isAddDialogOpen}
            onOpenChange={setIsAddDialogOpen}
            onTaskCreated={handleTaskCreated}
            providers={providers}
          />
        )}

        {isAiDialogOpen && (
          <AiMaintenanceDialog
            open={isAiDialogOpen}
            onOpenChange={setIsAiDialogOpen}
            onTasksCreated={handleAiTasksCreated}
            providers={providers}
          />
        )}

        {selectedTask && (
          <MaintenanceDetailsDialog
            open={isDetailsDialogOpen}
            onOpenChange={setIsDetailsDialogOpen}
            task={selectedTask}
            onStatusChange={handleStatusChange}
            onDeleteTask={handleDeleteTask}
            onTaskUpdated={handleTaskUpdated}
            providers={providers}
          />
        )}

        <div className="fixed bottom-16 right-4 sm:hidden">
          <Button
            size="icon"
            className="h-12 w-12 rounded-full shadow-lg"
            onClick={handleOpenAddDialog}
          >
            <Plus className="h-6 w-6" />
          </Button>
        </div>
      </div>
    </PageTransition>
  );
};

export default Maintenance;
